import { Container } from '@chakra-ui/react';
import { ImageTitleBanner } from '@components/ImageTitleBanner';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

function UploadDocsBanner() {
  const { DOCUMENTS, PERSONAL } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;
  return (
    <Container
      paddingInlineStart={{ base: '0 !important', md: '4rem !important' }}
      paddingInlineEnd="0 !important"
      padding={{ base: '4rem' }}
      margin="2rem"
      marginInlineStart="0px"
      marginInlineEnd="0px"
      position="relative"
      maxWidth="100%"
      minHeight={{ md: '750px' }}
      display="flex"
      justifyContent="center"
      alignItems="center"
    >
      <ImageTitleBanner
        title="Tired of maintaining a paper mountain of records?"
        description="Digitise your health records with Fluent. Easily scan, upload, email and organise them for quick access anytime, anywhere."
        image="/upload-records-cover.png" // TODO - Check it's come's from cms
        cta={{
          text: 'Add Records',
          href: `/${DOCUMENTS}/${PERSONAL}/${VIEW}`,
        }}
        reverse
        mainContainerStyles={{
          position: 'static',
          maxWidth: '1200px',
        }}
        containerStyles={{
          marginLeft: { md: '-2rem' },
        }}
        imageStyles={{
          position: { base: 'static', md: 'absolute' },
          top: 100,
          right: 0,
        }}
      />
    </Container>
  );
}

export default UploadDocsBanner;
