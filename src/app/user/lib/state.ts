import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useLocalStorage } from 'usehooks-ts';

import { MasterLifestyleQuestion } from '@lib/models/lifestyle-and-nutrition';
import { PATIENT_PROGRESS_FIELDS_MAP } from './constants';
import { LOCAL_STORAGE_KEYS } from '@lib/constants';
import { useAuthService } from '@lib/state';

export enum LIFESTYLE_QUESTION_FIELD_TYPES {
  select,
  multiSelect,
}

export const LIFESTYLE_QUESTION_FIELD_TYPE_MAP: Record<MasterLifestyleQuestion['id'], LIFESTYLE_QUESTION_FIELD_TYPES> =
  {
    'lifestyle-nutrition-exercise-activities-you-take-part': LIFESTYLE_QUESTION_FIELD_TYPES.multiSelect,
    'lifestyle-nutrition-diet-any-other-diet-preferences-restrictions': LIFESTYLE_QUESTION_FIELD_TYPES.multiSelect,
    'lifestyle-nutrition-mental-health-sleep-personal-history-mental-health-conditions':
      LIFESTYLE_QUESTION_FIELD_TYPES.multiSelect,
    'lifestyle-nutrition-mental-health-sleep-family-history-mental-health-conditions':
      LIFESTYLE_QUESTION_FIELD_TYPES.multiSelect,
  };
export const usePatientProgress = () => {
  const queryClient = useQueryClient();
  const { authenticatedUser } = useAuthService();

  const initialValues = {
    [PATIENT_PROGRESS_FIELDS_MAP.ETHNICITY]: 0,
  };

  const [localValues, setPatientProgress] = useLocalStorage(
    `[${LOCAL_STORAGE_KEYS.PATIENT_PROGRESS}, ${authenticatedUser?.id}]`,
    initialValues
  );

  const getInitialValues = () => localValues;
  const { data } = useQuery([LOCAL_STORAGE_KEYS.PATIENT_PROGRESS, authenticatedUser?.id], getInitialValues, {
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
    staleTime: Infinity,
    initialData: localValues,
    suspense: true,
  });

  const updatePatientProgress = (payload: Record<any, any>) => {
    const result: any = localValues;
    Object.keys(payload).forEach((key) => {
      if (result[key] === 0) {
        result[key] = payload[key];
      }
    });
    return result;
  };
  const { mutateAsync: mutateUpdatePatientProgress } = useMutation(updatePatientProgress, {
    onSuccess: (response: Record<string, any>) => {
      queryClient.setQueryData([LOCAL_STORAGE_KEYS.PATIENT_PROGRESS, authenticatedUser?.id], response);
      setPatientProgress(response);
    },
  });

  return {
    patientProgress: data,
    updatePatientProgress: mutateUpdatePatientProgress,
  };
};
