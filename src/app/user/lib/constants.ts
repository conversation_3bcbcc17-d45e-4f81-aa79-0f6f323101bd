import { MEDICAL_RECORD_TYPES } from '@lib/models/medical-record';
import { FH_CODE_SYSTEM_FACT, SNOMED_URL } from 'src/constants/medplumConstants';

export const SUMMARY_CARD_TYPES = {
  RECORDS_IN_REVIEW: 'recordsInReview',
  REMINDERS: 'reminders',
  PROFILE_COMPLETION: 'profileCompletion',
};

export type SummaryCardType =
  | typeof SUMMARY_CARD_TYPES.RECORDS_IN_REVIEW
  | typeof SUMMARY_CARD_TYPES.REMINDERS
  | typeof SUMMARY_CARD_TYPES.PROFILE_COMPLETION;

export const LINKED_DOCUMENTS_MOCK_DATA = [
  {
    id: '0',
    title: 'Title of consultation',
    date: new Date().toDateString(),
    type: {
      id: '0',
      slug: MEDICAL_RECORD_TYPES.CONSULTATION_NOTE,
      text: 'Consultation',
    },
  },
  {
    id: '1',
    title: 'Title of lab results',
    date: new Date().toDateString(),
    type: {
      id: '1',
      slug: MEDICAL_RECORD_TYPES.LAB_RESULTS,
      text: 'Lab Result',
    },
  },
];

export enum ROUTE_ACTIONS {
  ADD = 'add',
  EDIT = 'edit',
  VIEW = 'view',
  DELETE = 'delete',
}

export enum CARE_TEAM_ACTION {
  DELETE_REFERENCE = 'delete:care-team:practitioner:reference',
  NON_FLUENT_PRACTITIONER_ID = '39e96537-eb2c-494b-9527-e335523cfe87',
}

export enum ROUTE_VARIABLES {
  // HOME
  DASHBOARD = 'dashboard',

  // PROFILE
  PROFILE = 'profile',
  EHR = 'ehr',
  BASIC = 'basics',
  FAMILY_HISTORY = 'family-history',
  CARE_TEAM = 'care-team',

  // SETTINGS
  SETTINGS = 'settings',
  FLUENT_SUPPORT = 'fluent-support',
  FAQS = 'faqs',
  SOCIAL = 'social',
  MEASUREMENT = 'measurement',
  COMMUNICATION = 'communication',
  PERSONAL_DATA_USAGE = 'personal-data-usage',
  LEGAL = 'legal',
  PRIVACY_POLICY = 'privacy-policy',
  TERMS_N_CONDITIONS = 'terms-n-conditions',
  COOKIE_POLICY = 'cookie-policy',
  COMMUNICATIONS_CONSENT = 'communications-consent',
  MEDICAL_ADVICE_DISCLAIMER = 'medical-advice-disclaimer',

  // HELP
  HELP = 'help',

  // DOCUMENTS
  DOCUMENTS = 'documents',
  USERS = 'users',
  UPLOAD = 'upload',
  OTHERS = 'others',
  REVIEW = 'review',
  RECENT = 'recent',
  PERSONAL = 'personal',
  USER_METADATA = 'usermetadata',

  // HEALTH PROFILE
  EMERGENCY_CONTACTS = 'emergency-contacts',
  HEALTHCARE_PROXY = 'healthcare-proxy',
  HEALTH_INSURANCE = 'health-insurance',
  CONDITIONS = 'conditions',
  SYMPTOMS = 'symptoms',
  MEDICATIONS_SUPPLEMENTS = 'medications-supplements',
  MEDICATION = 'medication',
  SUPPLEMENT = 'supplement',
  ALLERGIES = 'allergies',
  PROCEDURES = 'procedures',
  LIFESTYLE_NUTRITION = 'lifestyle-nutrition',
  REPRODUCTIVE_HEALTH = 'reproductive-health',
  PREVENTATIVE_SCREENING = 'preventative-screening',
  VACCINES = 'vaccines',
  VITALS = 'vitals',
  EXERCISE = 'exercise',
  DIET = 'diet',
  ALCOHOL_TOBACCO = 'alcohol-tobacco',
  MENTAL_HEALTH = 'mental-health',
  OCCUPATION = 'occupation',
  ANNUAL_PHYSICAL = 'annual-physical',
  EYE_EXAMINATION = 'eye-examination',
  DENTAL_CHECKUP_CLEANING = 'dental-checkup-cleaning',
  SKIN_SELF_EXAMINATION = 'skin-self-examination',
  BREAST_SELF_EVALUATION = 'breast-self-evaluation',
  TESTICULAR_SELF_EXAMINATION = 'testicular-self-examination',
  COMPLETE_BLOOD_COUNT = 'cbc',
  THYROID_TEST = 'thyroid-test',
  BLOOD_SUGAR_TEST = 'blood-sugar-test',
  LIPID_PROFILE = 'lipid-profile',
  ALLERGY_TEST = 'allergy-test',
  PROSTATE_SPECIFIC_ANTIGEN_PSA = 'psa',
  PAP_SMEAR = 'pap-smear',
  MAMMOGRAM = 'mammogram',
  ELECTROCARDIOGRAM_ECG = 'ecg',
  TWO_D_ECHOCARDIOGRAM = '2d-echocg',
  TREADMILL_TEST_TMT = 'tmt',
  COLONOSCOPY = 'colonoscopy',
  SEXUALLY_TRANSMITTED_INFECTIONS_STI = 'sti',
  BLOOD_PRESSURE = 'blood-pressure',
  BODY_TEMPERATURE = 'body-temperature',
  PULSE_RATE = 'pulse-rate',
  OXYGEN_SATURATION_LEVEL = 'oxygen-saturation',
  RESPIRATORY_RATE = 'respiratory-rate',
}

export const NavigationHelper = {
  isDesktop: () => window.matchMedia('(min-width: 1024px)').matches,
  getBasicsView: (includeProfile = true, { absolutePath = false, midPath = false } = {}): string => {
    const absolutePrefix = absolutePath || !NavigationHelper.isDesktop() ? '/' : '';
    const profilePath = includeProfile || !NavigationHelper.isDesktop() ? `${ROUTE_VARIABLES.PROFILE}/` : '';
    const midPathAdd = midPath && !NavigationHelper.isDesktop() ? `${ROUTE_VARIABLES.EHR}` : `${ROUTE_VARIABLES.BASIC}`;
    return `${absolutePrefix}${profilePath}${midPathAdd}/view`;
  },
  getEhrView: (includeProfile: boolean = true, ...subPaths: (string | null | undefined)[]): string => {
    const absolutePrefix = !NavigationHelper.isDesktop() ? '/' : '';
    const profilePrefix = includeProfile || !!absolutePrefix ? `${ROUTE_VARIABLES.PROFILE}/` : '';
    const ehrPrefix = `${ROUTE_VARIABLES.EHR}/`;
    const filteredSubPaths = subPaths.filter(Boolean).join('/'); // Remove null/undefined values
    return `${absolutePrefix}${profilePrefix}${ehrPrefix}${filteredSubPaths}/view`;
  },
};
export const PATIENT_PROGRESS_FIELDS_MAP = {
  HEIGHT: 'height',
  WEIGHT: 'weight',
  BLOOD_GROUP: 'bloodGroup',
  CONDITIONS: 'Conditions',
  SYMPTOMS: 'Symptoms',
  MEDICATION: 'Medications and Supplements',
  ALLERGIES_INTOLERANCES: 'Allergies and/or Intolerances',
  SURGERIES_PROCEDURES: 'Surgeries and/or Procedures',
  LIFESTYLE_NUTRITION: 'Lifestyle and Nutrition',
  REPRODUCTIVE_HEALTH: 'Reproductive health',
  PREVENTATIVE_SCREENINGS: 'Key Health Tests',
  IMMUNIZATIONS: 'Vaccines',
  ETHNICITY: 'ethnicity',
  PREFERRED_LANGUAGE: 'preferredLanguage',
  RELATED_PERSONS: 'relatedPersonList',
};

export const MY_HEALTH_PROFILE_MAP = {
  conditions: { name: 'Conditions', route: ROUTE_VARIABLES.CONDITIONS },
  symptoms: { name: 'Symptoms', route: ROUTE_VARIABLES.SYMPTOMS },
  medication_supplement: { name: 'Medications and Supplements', route: ROUTE_VARIABLES.MEDICATIONS_SUPPLEMENTS },
  allergies_intolerance: { name: 'Allergies and/or Intolerances', route: ROUTE_VARIABLES.ALLERGIES },
  surgeries_procedure: { name: 'Surgeries and/or Procedures', route: ROUTE_VARIABLES.PROCEDURES },
  lifestyle_nutrition: { name: 'Lifestyle and Nutrition', route: ROUTE_VARIABLES.LIFESTYLE_NUTRITION },
  reproductive_health: { name: 'Reproductive health', route: ROUTE_VARIABLES.REPRODUCTIVE_HEALTH },
  preventative_screenings: { name: 'Key Health Tests', route: ROUTE_VARIABLES.PREVENTATIVE_SCREENING },
  vaccines: { name: 'Vaccines', route: ROUTE_VARIABLES.VACCINES },
  vitals: { name: 'Vitals', route: ROUTE_VARIABLES.VITALS },
  // ViewEditHistory: 'View Edit History',
};
export const VIEW_EDIT_HISTORY = 'View Edit History';
export const MEDICATION_SUPPLEMENT = {
  MEDICATION: 'Medication',
  SUPPLEMENT: 'Supplement',
};

export const LIFESTYLE_NUTRITION = [
  {
    name: 'LifestyleNutritionExercise',
    value: 'Exercise',
    route: ROUTE_VARIABLES.EXERCISE,
  },
  {
    name: 'LifestyleNutritionDiet',
    value: 'Diet',
    route: ROUTE_VARIABLES.DIET,
  },
  {
    name: 'LifestyleNutritionAlcoholTobaccoCaffeine',
    value: 'Alcohol/Tobacco/Caffeine Consumption',
    route: ROUTE_VARIABLES.ALCOHOL_TOBACCO,
  },
  {
    name: 'LifestyleNutritionMentalHealthSleep',
    value: 'Mental Health and Sleep',
    route: ROUTE_VARIABLES.MENTAL_HEALTH,
  },
  {
    name: 'LifestyleNutritionOccupation',
    value: 'Occupation',
    route: ROUTE_VARIABLES.OCCUPATION,
  },
];

export const PREVENTATIVE_SCREENING = {
  annual_physical: { name: 'PSAnnualPhysical', value: 'Annual Physical', route: ROUTE_VARIABLES.ANNUAL_PHYSICAL },
  eye_examination: { name: 'PSEyeExamination', value: 'Eye Examination', route: ROUTE_VARIABLES.EYE_EXAMINATION },
  dental_checkup: {
    name: 'PSDentalCheckupCleaning',
    value: 'Dental Check-up & Cleaning',
    route: ROUTE_VARIABLES.DENTAL_CHECKUP_CLEANING,
  },
  skin_self_examination: {
    name: 'PSSkinSelfExamination',
    value: 'Skin Self-examination',
    route: ROUTE_VARIABLES.SKIN_SELF_EXAMINATION,
  },
  breast_selF_evaluation: {
    name: 'PSBreastSelfEvaluation',
    value: 'Breast Self-evaluation',
    route: ROUTE_VARIABLES.BREAST_SELF_EVALUATION,
  },
  testicular_self_examination: {
    name: 'PSTesticularSelfExamination',
    value: 'Testicular Self-examination',
    route: ROUTE_VARIABLES.TESTICULAR_SELF_EXAMINATION,
  },
  complete_blood_count: {
    name: 'PSCompleteBloodCount',
    value: 'Complete Blood Count (CBC)',
    route: ROUTE_VARIABLES.COMPLETE_BLOOD_COUNT,
  },
  thyroid_test: { name: 'PSThyroidTest', value: 'Thyroid Test', route: ROUTE_VARIABLES.THYROID_TEST },
  blood_sugar_test: { name: 'PSBloodSugarTest', value: 'Blood Sugar Test', route: ROUTE_VARIABLES.BLOOD_SUGAR_TEST },
  lipid_profile: { name: 'PSLipidProfile', value: 'Lipid Profile', route: ROUTE_VARIABLES.LIPID_PROFILE },
  allergy_test: { name: 'PSAllergyTest', value: 'Allergy Test', route: ROUTE_VARIABLES.ALLERGY_TEST },
  prostate_specific_antigen_psa: {
    name: 'PSProstateSpecificAntigenPSA',
    value: 'Prostate-specific Antigen (PSA)',
    route: ROUTE_VARIABLES.PROSTATE_SPECIFIC_ANTIGEN_PSA,
  },
  pap_smear: { name: 'PSPapSmear', value: 'Pap Smear', route: ROUTE_VARIABLES.PAP_SMEAR },
  mammogram: { name: 'PSMammogram', value: 'Mammogram', route: ROUTE_VARIABLES.MAMMOGRAM },
  electrocardiogram_ecg: {
    name: 'PSElectrocardiogramECG',
    value: 'Electrocardiogram (ECG or EKG)',
    route: ROUTE_VARIABLES.ELECTROCARDIOGRAM_ECG,
  },
  two_d_echocardiogram: {
    name: 'PS2DEchocardiogram',
    value: '2D Echocardiogram',
    route: ROUTE_VARIABLES.TWO_D_ECHOCARDIOGRAM,
  },
  treadmill_test_tmt: {
    name: 'PSTreadmillTestTMT',
    value: 'Treadmill Test (TMT)',
    route: ROUTE_VARIABLES.TREADMILL_TEST_TMT,
  },
  colonoscopy: { name: 'PSColonoscopy', value: 'Colonoscopy', route: ROUTE_VARIABLES.COLONOSCOPY },
  sexually_transmitted_infections_sti: {
    name: 'PSSexuallyTransmittedInfectionsSTI',
    value: 'Sexually Transmitted Infections (STI)',
    route: ROUTE_VARIABLES.SEXUALLY_TRANSMITTED_INFECTIONS_STI,
  },
};
export const PREVENTATIVE_SCREENING_LINK_ID: any = {
  PSAnnualPhysical: {
    type: 'ps:annualphysical',
    dateScreening: 'ps-annual-physical-date-screening',
    reference: 'ps-annual-physical-reference',
  },
  PSEyeExamination: {
    type: 'ps:eyeexamination',
    dateScreening: 'ps-eye-examination-date-screening',
    reference: 'ps-eye-examination-reference',
  },
  PSDentalCheckupCleaning: {
    type: 'ps:dentalcheckupcleaning',
    dateScreening: 'ps-dental-checkup-cleaning-date-screening',
    reference: 'ps-dental-checkup-cleaning-reference',
  },
  PSSkinSelfExamination: {
    type: 'ps:skinselfexamination',
    dateScreening: 'ps-skin-self-examination-date-screening',
  },
  PSBreastSelfEvaluation: {
    type: 'ps:breastselfevaluation',
    dateScreening: 'ps-breast-self-evaluation-date-screening',
  },
  PSTesticularSelfExamination: {
    type: 'ps:testicularselfexamination',
    dateScreening: 'ps-testicular-self-examination-date-examination',
  },
  PSCompleteBloodCount: {
    type: 'ps:completebloodcount',
    dateScreening: 'ps-complete-blood-count-date-screening',
    reference: 'ps-complete-blood-count-reference',
  },
  PSThyroidTest: {
    type: 'ps:thyroidtest',
    dateScreening: 'ps-thyroid-test-date-screening',
    reference: 'ps-thyroid-test-reference',
  },
  PSBloodSugarTest: {
    type: 'ps:bloodsugartest',
    dateScreening: 'ps-blood-sugar-date-screening',
    reference: 'ps-blood-sugar-reference',
  },
  PSLipidProfile: {
    type: 'ps:lipidprofile',
    dateScreening: 'ps-lipid-profile-date-screening',
    reference: 'ps-lipid-profile-reference',
    globalQuestionnaire: 'preventative-screening-lipid-profile',
  },
  PSAllergyTest: {
    type: 'ps:allergytest',
    dateScreening: 'ps-allergy-test-date-screening',
    reference: 'ps-allergy-test-reference',
  },
  PSProstateSpecificAntigenPSA: {
    type: 'ps:prostatespecificantigen',
    dateScreening: 'ps-prostate-specific-antigen-date-screening',
    reference: 'ps-prostate-specific-antigen-reference',
    globalQuestionnaire: 'preventative-screening-prostate-specific-antigen',
  },
  PSPapSmear: {
    type: 'ps:papsmear',
    dateScreening: 'ps-pap-smear-date-screening',
    reference: 'ps-pap-smear-reference',
    globalQuestionnaire: 'preventative-screening-pap-smear-cervical-cancer',
    globalQuestionnaire2: 'preventative-screening-pap-smear-hpv',
  },
  PSMammogram: {
    type: 'ps:mammogram',
    dateScreening: 'ps-mammogram-date-screening',
    reference: 'ps-mammogram-reference',
    globalQuestionnaire: 'preventative-screening-mammogram',
  },
  PSElectrocardiogramECG: {
    type: 'ps:electrocardiogram',
    dateScreening: 'ps-electrocardiogram-ecg-ekg-date-screening',
    reference: 'ps-electrocardiogram-ecg-ekg-reference',
  },
  PS2DEchocardiogram: {
    type: 'ps:twodechocardiogram',
    dateScreening: 'ps-2D-echocardiogram-date-screening',
    reference: 'ps-2D-echocardiogram-reference',
  },
  PSTreadmillTestTMT: {
    type: 'ps:treadmilltest',
    dateScreening: 'ps-treadmill-test-tmt-date-screening',
    reference: 'ps-treadmill-test-tmt-reference',
  },
  PSColonoscopy: {
    type: 'ps:colonoscopy',
    dateScreening: 'ps-colonoscopy-date-screening',
    reference: 'ps-colonoscopy-reference',
  },
  PSSexuallyTransmittedInfectionsSTI: {
    type: 'ps:sexuallytransmittedinfections',
    dateScreening: 'ps-sexually-transmitted-infections-date-screening',
    reference: 'ps-sexually-transmitted-infections-reference',
    globalQuestionnaire: 'preventative-sexually-transmitted-infections',
  },
};

const CATEGORY = {
  system: SNOMED_URL,
  code: '103693007',
  display: 'Diagnostic procedure',
};

export const preventativeScreeningConstants = [
  {
    annualPhysical: {
      type: 'Annual Physical',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:annual-physical',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '78318003',
        display: 'History and physical examination, annual for health maintenance (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    eyeExamination: {
      type: 'Eye Examination',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:eye-examination',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '36228007',
        display: 'Ophthalmic examination and evaluation (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    dentalCheckup: {
      type: 'Dental Check-up & Cleaning',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:dental-checkup-cleaning',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '34043003',
        display: 'Dental consultation and report (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    skinSelfExamination: {
      type: 'Skin Self-examination',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:skin-self-examination',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '409980007',
        display: 'Skin self-examination (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    breastSelfEvaluation: {
      type: 'Breast Self-evaluation',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:breast-self-evaluation',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '409979009',
        display: 'Breast Self-evaluation (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    testicularSelfExamination: {
      type: 'Testicular Self-examination',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:testicular-self-examination',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '163382007',
        display: 'Testicular self-examination (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    completeBloodCount: {
      type: 'Complete Blood Count (CBC)',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:complete-blood-count',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '26604007',
        display: 'Complete blood count (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    thyroidTest: {
      type: 'Thyroid Test',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:thyroid-test',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '35650009',
        display: 'Thyroid panel (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    bloodSugarTest: {
      type: 'Blood Sugar Test',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:blood-sugar-test',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '309093003',
        display: 'Blood glucose measurement (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    lipidProfile: {
      type: 'Lipid Profile',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:lipid-profile',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '20917003',
        display: 'Lipid panel (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    allergyTest: {
      type: 'Allergy Test',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:allergy-test',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '30951000119106',
        display: 'Allergy test (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    prostateSpecificAntigen: {
      type: 'Prostate-specific Antigen (PSA)',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:prostate-specific-antigen',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '48961001',
        display: 'Prostate-specific antigen test (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    papSmear: {
      type: 'Pap Smear',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:pap-smear',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '408734003',
        display: 'Pap smear (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    mammogram: {
      type: 'Mammogram',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:mammogram',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '56100005',
        display: 'Mammogram (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    electrocardiogram: {
      type: 'Electrocardiogram (ECG or EKG)',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:electrocardiogram',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '36225008',
        display: 'Electrocardiogram (ECG or EKG) (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    twoDEchocardiogram: {
      type: '2D Echocardiogram',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:two-d-echocardiogram',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '16989001',
        display: 'Echocardiogram (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    treadmillTest: {
      type: 'Treadmill Test (TMT)',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:treadmill-test',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '28423006',
        display: 'Exercise electrocardiogram (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    colonoscopy: {
      type: 'Colonoscopy',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:colonoscopy',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '274527003',
        display: 'Colonoscopy (procedure)',
      },
      category: CATEGORY,
    },
  },
  {
    sexuallyTransmittedInfections: {
      type: 'Sexually Transmitted Infections (STI)',
      identifier: {
        system: FH_CODE_SYSTEM_FACT,
        value: 'ps:sexually-transmitted-infections',
      },
      snowMed: {
        system: 'http://snomed.info/sct',
        code: '171233003',
        display: 'Genitourinary disease screening (procedure)',
      },
      category: CATEGORY,
    },
  },
];

export const VITALS = {
  BloodPressure: {
    label: 'Blood Pressure',
    key: 'vi:blood-pressure',
    code: '85354-9',
    unit: 'mmHg',
    route: ROUTE_VARIABLES.BLOOD_PRESSURE,
  },
  BodyTemperature: {
    label: 'Body temperature',
    key: 'vi:body-temperature',
    code: '8310-5',
    unit: '°F',
    route: ROUTE_VARIABLES.BODY_TEMPERATURE,
  },
  PulseRate: {
    label: 'Pulse Rate',
    key: 'vi:pulse-rate',
    code: '8867-4',
    unit: 'Beats Per Minute',
    route: ROUTE_VARIABLES.PULSE_RATE,
  },
  OxygenSaturationLevel: {
    label: 'Oxygen Saturation Level (spO2)',
    key: 'vi:oxygen-saturation-level',
    code: '59408-5',
    unit: 'Percent',
    route: ROUTE_VARIABLES.OXYGEN_SATURATION_LEVEL,
  },
  RespiratoryRate: {
    label: 'Respiratory Rate',
    key: 'vi:respiratory-rate',
    code: '9279-1',
    unit: 'Breaths Per Minute',
    route: ROUTE_VARIABLES.RESPIRATORY_RATE,
  },
} as const;

export const PATIENT_PROGRESS_DEFAULT_VALUE = 40;
export const LOINC_ORG = 'http://loinc.org';

export const PATIENT_PROGRESS_PERCENTAGE_VALUES_MAP = {
  [PATIENT_PROGRESS_FIELDS_MAP.HEIGHT]: 3,
  [PATIENT_PROGRESS_FIELDS_MAP.WEIGHT]: 3,
  [PATIENT_PROGRESS_FIELDS_MAP.BLOOD_GROUP]: 2,
  [PATIENT_PROGRESS_FIELDS_MAP.CONDITIONS]: 10,
  [PATIENT_PROGRESS_FIELDS_MAP.MEDICATION]: 12,
  [PATIENT_PROGRESS_FIELDS_MAP.SURGERIES_PROCEDURES]: 8,
  [PATIENT_PROGRESS_FIELDS_MAP.ALLERGIES_INTOLERANCES]: 5,
  [PATIENT_PROGRESS_FIELDS_MAP.LIFESTYLE_NUTRITION]: 10,
  [PATIENT_PROGRESS_FIELDS_MAP.REPRODUCTIVE_HEALTH]: 8,
  [PATIENT_PROGRESS_FIELDS_MAP.PREVENTATIVE_SCREENINGS]: 6,
  [PATIENT_PROGRESS_FIELDS_MAP.IMMUNIZATIONS]: 6,
  [PATIENT_PROGRESS_FIELDS_MAP.ETHNICITY]: 2,
  [PATIENT_PROGRESS_FIELDS_MAP.PREFERRED_LANGUAGE]: 1,
  [PATIENT_PROGRESS_FIELDS_MAP.RELATED_PERSONS]: 10,
};

export const DATE_MONTH_YEAR = 'D MMM. YYYY';
export const ICD10 = {
  CONCEPT_KEY: 'concept',
  MATCH_KEY: 'match',
};
export const DOCUMENT_REF = 'DocumentReference';
export const PATIENT_DEFINED = 'patientdefined';

export enum enumStatus {
  PROCESSING = 'Processing',
  PROCESSING_PAUSED = 'Processing Paused',
  REJECTED = 'Rejected',
}

export const waitlistUrl = import.meta.env.VITE_WAITLIST_URL;

export enum enumUnit {
  KG = 'kg',
  POUNDS = '[lb_av]',
  POUNDS_TEXT = 'lbs',
  INCHES = '[in_i]',
  INCHES_TEXT = 'inches',
  CM = 'cm',
}
export enum enumMesurment {
  HEIGHT = 'Height',
  WEIGHT = 'Weight',
  BODY_HEIGHT = 'body height',
  BODY_WEIGHT = 'body weight',
  HEIGHT_UNIT = 'heightUnit',
  WEIGHT_UNIT = 'weightUnit',
}
