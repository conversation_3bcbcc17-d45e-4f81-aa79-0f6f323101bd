import { useState } from 'react';
import {
  Avatar,
  Box,
  Button,
  Container,
  Flex,
  Heading,
  Stack,
  SystemStyleObject,
  Tag,
  TagProps,
  Text,
  TextProps,
} from '@chakra-ui/react';
import { ChatIcon } from '@chakra-ui/icons';
import dayjs from 'dayjs';

import { SidebarCloseButton } from 'src/app/user/profile/components/SidebarComponents';
import { Coding, PractitionerQualification } from 'src/gql/graphql';
import { FHIR_URL } from '@lib/constants';

export const ELLIPSIS_STYLE: SystemStyleObject = {
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
};
export const TWO_ELLIPSIS_STYLE: SystemStyleObject = {
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  display: '-webkit-box',
  WebkitLineClamp: 2,
  WebkitBoxOrient: 'vertical',
};
export function InternalHeader({ children, ...props }: TextProps) {
  return (
    <Text
      fontWeight="500"
      textTransform="uppercase"
      fontSize="11px"
      color="gray.500"
      letterSpacing="1.32px"
      {...props}
    >
      {children}
    </Text>
  );
}

function LocationIcon({ width = 17, height = 16, fill = 'none', stroke = '#14181A', ...props }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 17 16"
      fill={fill}
      {...props}
    >
      <path
        d="M12.5 7.09091C12.5 10.2727 8.5 13 8.5 13C8.5 13 4.5 10.2727 4.5 7.09091C4.5 6.00593 4.92143 4.96539 5.67157 4.1982C6.42172 3.43101 7.43913 3 8.5 3C9.56087 3 10.5783 3.43101 11.3284 4.1982C12.0786 4.96539 12.5 6.00593 12.5 7.09091Z"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.5 8C9.05228 8 9.5 7.55228 9.5 7C9.5 6.44772 9.05228 6 8.5 6C7.94772 6 7.5 6.44772 7.5 7C7.5 7.55228 7.94772 8 8.5 8Z"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
interface InternalTagProps extends TagProps {
  text: string;
}

export function InternalTag({ text, ...props }: InternalTagProps) {
  return (
    <Tag
      bg="white"
      size="lg"
      padding="8px 16px"
      fontSize="16px"
      borderRadius="full"
      variant="outline"
      colorScheme="orange"
      color="#000"
      fontWeight={400}
      lineHeight={0}
      {...props}
    >
      {text}
    </Tag>
  );
}

export default function CareInfoDrawer({ onClose, Patient, id }: any) {
  const CURRENT_PRACTITIONER = Patient.filter((i: any) => i.id === id)[0];
  const [showAll, setShowAll] = useState(false);
  // const calculateYearsOfExperience = () => {
  //   const qualifications = CURRENT_PRACTITIONER?.participant?.[0]?.member?.resource?.qualification;
  //   if (!qualifications) return null;
  //   const officialQualification = qualifications.find((q: any) =>
  //     q.extension?.some((ext: any) => ext.url === "https://fluentinhealth.com/fhir/StructureDefinition/PractitionerQualificationIsYearOfExperience" && ext.valueBoolean)
  //   );

  //   if (!officialQualification?.period?.start) return null;
  //   const startDate = new Date(officialQualification.period.start);
  //   const currentDate = new Date();
  //   const yearsOfExperience = (currentDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 365);
  //   return Math.round(yearsOfExperience);
  // };

  // const experience = calculateYearsOfExperience();
  // console.log(experience);

  return (
    <Container
      position="relative"
      overflowY="scroll"
      overflowX="hidden"
      bg="#FFF3F0"
      height="full"
      px={0}
    >
      <Box
        bg="#FFE0D6"
        p={8}
        borderRadius="lg"
        textAlign="center"
        maxW="100%"
        position="relative"
        _before={{
          content: `""`,
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: `url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="334" height="254" fill="none" stroke="%23ff6333"><path opacity=".3" d="M333.5 261.5c0 92.788-74.549 168-166.5 168S.5 354.288.5 261.5s74.549-168 166.5-168 166.5 75.212 166.5 168zm0-354c0 92.788-74.549 168-166.5 168S.5.288.5-92.5s74.549-168 166.5-168 166.5 75.212 166.5 168z"/></svg>')`,
          backgroundSize: '80% 100%',
          zIndex: 0,
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      >
        <Box
          position="relative"
          zIndex={1}
        >
          <SidebarCloseButton
            onClick={onClose}
            position="absolute"
            right="0"
            top="0"
          />
          {CURRENT_PRACTITIONER.participant[0].member?.resource?.photo && (
            <Flex
              justify="center"
              mb={5}
            >
              <Avatar
                size="xl"
                src={CURRENT_PRACTITIONER.participant[0].member.resource.photo[0].url}
                name="Doc Name"
              />
            </Flex>
          )}
          {(() => {
            const { participant } = CURRENT_PRACTITIONER;
            const memberResource = participant?.[0]?.member?.resource;
            if (!memberResource?.name) return null;
            const { given, family } = memberResource.name[0];
            const identifier = memberResource.identifier?.[0];
            const officialCode = identifier?.use === 'official' ? `, ${identifier?.type?.coding?.[0]?.code}` : '';
            return (
              <Heading
                size="xl"
                fontSize="24px"
                color="#000"
                mt={3}
                sx={ELLIPSIS_STYLE}
              >
                {`${given} ${family}${officialCode}`}
              </Heading>
            );
          })()}

          {CURRENT_PRACTITIONER?.participant?.[0]?.member?.resource?.identifier?.[0]?.use === 'official' &&
            (() => {
              const { assigner, type, value } = CURRENT_PRACTITIONER.participant[0].member.resource.identifier[0];
              const { coding } = type || {};
              const codeDisplay = coding?.[0]?.display;
              const code = coding?.[0]?.code;
              return (
                <Text
                  fontSize="16px"
                  mt={2}
                  sx={TWO_ELLIPSIS_STYLE}
                >
                  {assigner?.display}, {code}, {codeDisplay}, {value}
                </Text>
              );
            })()}

          {CURRENT_PRACTITIONER.participant[0]?.member?.resource?.address && (
            <Flex
              justify="center"
              gap="3px"
              alignItems="center"
              color="#14181A"
              fontWeight="400"
              mt={1}
              fontSize="16px"
              align="center"
            >
              <LocationIcon />
              {`${
                CURRENT_PRACTITIONER.participant[0]?.member?.resource?.address?.[0]?.cityText ||
                CURRENT_PRACTITIONER.participant[0]?.member?.resource?.address?.[0]?.city ||
                ' '
              }, ${
                CURRENT_PRACTITIONER.participant[0]?.member?.resource?.address?.[0]?.countryText ||
                CURRENT_PRACTITIONER.participant[0]?.member?.resource?.address?.[0]?.country ||
                ' '
              }`}
            </Flex>
          )}
        </Box>
      </Box>
      <Container
        position="relative"
        py={5}
      >
        <Stack
          maxWidth="400px"
          height="full"
        >
          <InternalHeader>Consult Availability</InternalHeader>
          <Flex
            wrap="wrap"
            gap="10px"
            pb={3}
          >
            {['HardCode:', 'Mon 10 AM – 5 PM', 'Mon 10 AM – 7 PM', 'Fri 10 AM – 6 PM'].map((text) => (
              <InternalTag
                key={text}
                text={text}
              />
            ))}
          </Flex>

          {CURRENT_PRACTITIONER.participant?.[0]?.member?.resource?.practitionerRole?.[0]?.specialty?.[0]?.coding
            ?.length > 0 && (
            <>
              <InternalHeader>Specialty</InternalHeader>
              <Flex
                wrap="wrap"
                gap="10px"
                pb={3}
              >
                {CURRENT_PRACTITIONER.participant[0].member.resource.practitionerRole[0].specialty[0].coding.map(
                  (text: Coding) =>
                    text.display ? (
                      <InternalTag
                        key={text?.code}
                        text={text.display}
                      />
                    ) : null
                )}
              </Flex>
            </>
          )}

          {CURRENT_PRACTITIONER.participant[0].member?.resource?.communication && (
            <>
              <InternalHeader>Languages</InternalHeader>
              <Flex
                wrap="wrap"
                gap="10px"
                pb={3}
              >
                {CURRENT_PRACTITIONER.participant[0].member.resource.communication[0].coding.map((text: Coding) =>
                  text.display ? (
                    <InternalTag
                      key={text?.code}
                      text={text?.display}
                    />
                  ) : null
                )}
              </Flex>
            </>
          )}

          <InternalHeader>Contact Information</InternalHeader>
          <Button
            leftIcon={<ChatIcon />}
            colorScheme="blue"
            color="fluentHealth.500"
            border="0"
            background="white"
            variant="outline"
            justifyContent="flex-start"
            borderRadius="10px"
            padding="8px 16px"
            onClick={onClose}
          >
            Chat with Fluent Support
          </Button>
          {CURRENT_PRACTITIONER?.participant?.[0]?.member?.resource?.extension?.[0]?.url ===
            `${FHIR_URL}/PractitionerProfile` && (
            <>
              <InternalHeader pt={3}>About Me</InternalHeader>
              <Box
                sx={{
                  padding: '17px 20px',
                  borderRadius: '10px',
                  border: '1.5px solid fluentHealthComplementary.Salmon3',
                  background: 'rgba(255, 193, 173, 0.30)',
                }}
              >
                {CURRENT_PRACTITIONER.participant?.[0]?.member?.resource?.extension?.[0]?.valueString}
              </Box>
            </>
          )}

          {(() => {
            const identifierPeriodEnd =
              CURRENT_PRACTITIONER?.participant?.[0]?.member?.resource?.identifier?.[0]?.period?.end;
            if (!identifierPeriodEnd) return null;
            const periodEndDate = new Date(identifierPeriodEnd);
            const currentDate = new Date();
            const diffInMilliseconds = currentDate.getTime() - periodEndDate.getTime();
            const diffInYears = diffInMilliseconds / (1000 * 60 * 60 * 24 * 365);
            const roundedExperience = Math.round(diffInYears);
            return (
              <>
                <InternalHeader pt={3}>Years of Experience</InternalHeader>
                <Flex
                  wrap="wrap"
                  gap="10px"
                  pb={3}
                >
                  <InternalTag text={roundedExperience.toString()} />
                </Flex>
              </>
            );
          })()}

          {CURRENT_PRACTITIONER.participant?.[0]?.member?.resource?.qualification?.length > 0 && (
            <>
              <InternalHeader>Education & Credentials</InternalHeader>
              <Box
                sx={{
                  padding: '17px 20px',
                  borderRadius: '10px',
                  background: 'white',
                }}
              >
                <Stack spacing={3}>
                  {(showAll
                    ? CURRENT_PRACTITIONER.participant[0].member.resource.qualification
                    : CURRENT_PRACTITIONER.participant[0].member.resource.qualification.slice(0, 3)
                  ).map((item: PractitionerQualification) => (
                    <Flex
                      key={item?.code?.coding?.[0]?.code}
                      wrap="nowrap"
                      gap="20px"
                    >
                      {item.period?.start && (
                        <Text
                          fontSize="16px"
                          color="gray.800"
                        >
                          {dayjs(item.period.start).format('YYYY')}
                        </Text>
                      )}
                      <Box>
                        {item.code?.text && (
                          <Text
                            fontWeight="700"
                            color="gray.800"
                            fontSize="16px"
                          >
                            {item.code.text}
                          </Text>
                        )}
                        {item.issuer?.display && (
                          <>
                            <Text
                              fontWeight="700"
                              color="gray.800"
                              fontSize="16px"
                            >
                              {item.code?.coding?.[0]?.display &&
                                `${item.code.coding[0].display}${
                                  item.code?.coding?.[0]?.code ? `, ${item.code.coding[0].code}` : ''
                                }`}
                            </Text>
                            <Text
                              fontWeight="400"
                              color="gray.800"
                              fontSize="16px"
                            >
                              {item.issuer.display}
                            </Text>
                          </>
                        )}
                      </Box>
                    </Flex>
                  ))}
                </Stack>
                {CURRENT_PRACTITIONER.participant[0].member.resource.qualification.length > 3 && (
                  <Text align="center">
                    <Button
                      size="sm"
                      variant="link"
                      colorScheme="blue"
                      mt={5}
                      onClick={() => setShowAll((prev) => !prev)}
                    >
                      {showAll ? 'Show Less' : 'Show More'}
                    </Button>
                  </Text>
                )}
              </Box>
            </>
          )}
        </Stack>
      </Container>
    </Container>
  );
}
