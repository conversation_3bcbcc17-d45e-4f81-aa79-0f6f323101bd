import {
  Card,
  CardBody,
  Container,
  Flex,
  Heading,
  Spacer,
  Stack,
  Text,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import React, { PropsWithChildren, Suspense, useCallback, useEffect, useMemo, useState } from 'react';
import { Edit3, Trash as TrashIcon } from 'react-feather';
import { medplumApi } from '@user/lib/medplum-api';
import { PATIENT_DEFINED } from '@user/lib/constants';
import dayjs from 'dayjs';
import { recordFamilyMemberHistoryEvents } from '@user/lib/events-analytics-manager';

import { ConditionType } from '@lib/models/condition';
import { RelatedPersonCondition } from '@lib/models/related-person';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { FormSkeleton } from 'src/components/ui/Form';
import { MODAL_VARIANTS, Modal, ModalProvider, useModal } from '../../../../../components/Modal';
import {
  MoreActionsMenu,
  MoreActionsMenuButton,
  MoreActionsMenuItem,
  MoreActionsMenuList,
} from '../../../../../components/ui/Menu';
import {
  ConsentModal,
  ConsentModalContent,
  ConsentModalFooter,
  ConsentModalHeading,
  ConsentModalPrimaryButton,
  ConsentModalSecondaryButton,
} from '../../components/ConsentModal';
import {
  RoundedTab,
  RoundedTabList,
  RoundedTabPanel,
  RoundedTabPanels,
  RoundedTabs,
} from '../../components/RoundedTabs';
import {
  CardHeading,
  SidebarAddButton,
  SidebarCloseButton,
  SidebarHelperTooltip,
} from '../../components/SidebarComponents';
import RelatedPersonConditionForm from './RelatedPersonConditionForm';
import { SidebarEmptyState } from '../../components/SidebarEmptyState';
import { EmptyStateTabsCard } from '../../components/EmptyStateTabsCard';
import { useFamilyMemberHistoryList } from 'src/app/user/lib/medplum-state';
import { LinkedDocumentsCard, LinkedDocumentsLabel } from '../../components/LinkedDocumentsCard';
import { FACT_CODE_SYSTEM } from '@lib/constants';
import { useExtractDocumentResource } from 'src/app/medical-records/lib/state';
import { removeNullValues } from '@lib/utils/utils';
import {
  FH_STRUCTURE_DEFINITION_CLINICALSTATUS,
  HL7_FAMILY_MEMBER_CONDITION_EVIDENCE,
} from 'src/constants/medplumConstants';

function ConditionCard({
  title,
  notes,
  diagnosedDate,
  children,
  onEdit,
  onRemove,
  isCustomEntry,
  isPublicMode = false,
}: PropsWithChildren<{
  title: string;
  notes: string;
  diagnosedDate: string | null;
  onEdit?: () => void;
  onRemove?: () => void;
  isCustomEntry?: boolean;
  isPublicMode?: boolean;
}>) {
  const deleteModal = useDisclosure();

  return (
    <>
      <ConsentModal {...deleteModal}>
        <ConsentModalHeading>
          Are you sure you want
          <br />
          to remove this entry?
        </ConsentModalHeading>
        <ConsentModalContent>This cannot be undone.</ConsentModalContent>
        <ConsentModalFooter>
          <ConsentModalSecondaryButton
            variant="quietDanger"
            color="red.100"
            onClick={() => {
              onRemove?.();
              deleteModal.onClose();
            }}
          >
            Remove
          </ConsentModalSecondaryButton>
          <ConsentModalPrimaryButton onClick={deleteModal.onClose}>Cancel</ConsentModalPrimaryButton>
        </ConsentModalFooter>
      </ConsentModal>
      <Card
        border="1px solid"
        borderRadius="xl"
        borderColor="#495AE4"
        boxShadow="none"
        mb="4"
        bgColor="none"
      >
        <CardBody
          px="4"
          pt="3"
          pb="3"
        >
          <Stack>
            <Flex justifyContent="space-between">
              <CardHeading maxWidth="90%">
                {title} {isCustomEntry && '[Custom entry]'}
              </CardHeading>
              {!isPublicMode && (
                <MoreActionsMenu>
                  <MoreActionsMenuButton />
                  <MoreActionsMenuList>
                    <MoreActionsMenuItem
                      icon={<Edit3 size={16} />}
                      onClick={onEdit}
                    >
                      Edit
                    </MoreActionsMenuItem>
                    <MoreActionsMenuItem
                      icon={<TrashIcon size={16} />}
                      onClick={() => deleteModal.onOpen()}
                    >
                      Delete
                    </MoreActionsMenuItem>
                  </MoreActionsMenuList>
                </MoreActionsMenu>
              )}
            </Flex>
            {diagnosedDate && (
              <Text
                bgColor="#495AE4"
                rounded="sm"
                width="fit-content"
                borderRadius="4px"
                px="1"
                color="#FFFFFF"
              >
                {diagnosedDate}
              </Text>
            )}
            {notes && <Text color="fluentHealthText.200">{notes}</Text>}
            {children}
          </Stack>
        </CardBody>
      </Card>
    </>
  );
}

export function SidebarConditions({ relatedPerson, onClose }: { relatedPerson: any; onClose: () => void }) {
  const toast = useToast();
  const [conditionOptions, setConditionOptions]: any = useState<any[]>([]);
  const [chronicityValue, setChronicityValue] = useState<any>();
  const [, setError] = useState<any>(null);
  const [selectedCondition, setSelectedCondition] = React.useState<RelatedPersonCondition | null>(null);
  const conditionModal = useModal();
  const { isPublicMode } = usePublicSettings();
  const { authenticatedUser } = useAuthService();
  const { addFamilyMemberCondition } = useFamilyMemberHistoryList(authenticatedUser?.id);
  const answerList = relatedPerson.condition || [];
  const { trackEventInFlow } = useAnalyticsService();

  useEffect(() => {
    Promise.all([
      medplumApi.valueSetList.getAllValueSetFromDirectus(isPublicMode, FACT_CODE_SYSTEM.CONDITIONS),
      medplumApi.valueSetList.getAllValueSetFromDirectus(isPublicMode, FACT_CODE_SYSTEM.CONDITIONS_COURSE),
    ]).then(([conditions, chronicity]) => {
      setConditionOptions(
        conditions.map((e: { display: string; code: string }) => ({ label: e.display, value: e.code }))
      );
      setChronicityValue(chronicity);
    });
  }, []);
  const chronicConditionList =
    answerList?.filter(
      (answer: RelatedPersonCondition) =>
        answer.extension?.length > 0 &&
        answer.extension.some((ext: { valueCodeableConcept?: { coding?: { display: string }[] } }) =>
          ext.valueCodeableConcept?.coding?.some((coding) => coding.display === ConditionType.Chronic)
        )
    ) || [];

  const acuteConditionList =
    answerList?.filter(
      (answer: RelatedPersonCondition) =>
        answer.extension?.length > 0 &&
        answer.extension.some((ext: any) =>
          ext.valueCodeableConcept?.coding?.some((coding: any) => coding.display === ConditionType.Acute)
        )
    ) || [];

  const onAddHandler = useCallback(() => {
    setSelectedCondition(null);
    conditionModal.modalDisclosure.onOpen();
  }, [conditionModal.modalDisclosure]);

  const onEditHandler = useCallback(
    (condition: RelatedPersonCondition) => {
      setSelectedCondition(condition);
      conditionModal.modalDisclosure.onOpen();
      recordFamilyMemberHistoryEvents(trackEventInFlow, {
        EventName: 'FMConditionInteracted',
      });
      recordFamilyMemberHistoryEvents(trackEventInFlow, {
        EventName: 'FMConditionEditStarted',
      });
    },
    [conditionModal.modalDisclosure]
  );
  const onRemoveHandler = async (familyMemberInfo: any, index: number) => {
    try {
      const updatedFamilyMember = {
        ...familyMemberInfo,
        resourceType: 'FamilyMemberHistory',
      };

      if (updatedFamilyMember.condition && index >= 0 && index < updatedFamilyMember.condition.length) {
        updatedFamilyMember.condition.splice(index, 1);
      }

      await addFamilyMemberCondition(removeNullValues(updatedFamilyMember));
      recordFamilyMemberHistoryEvents(trackEventInFlow, {
        EventName: 'FMConditionRemoved',
      });
      toast({
        title: 'Condition removed',
        description: 'Condition has been removed from the list',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (e) {
      setError(e);
    } finally {
      conditionModal.modalDisclosure.onClose();
    }
  };

  const getClinicalStatus = (c: any) =>
    c.extension?.find((ext: any) => ext.url === FH_STRUCTURE_DEFINITION_CLINICALSTATUS)?.valueCodeableConcept
      ?.coding?.[0]?.code;

  const formatDiagnosedDate = (condition: RelatedPersonCondition): string => {
    const { onsetPeriod } = condition || {};
    const startDate = onsetPeriod?.start;
    const endDate = onsetPeriod?.end;
    const clinicalStatus = getClinicalStatus(condition);
    if (!startDate) return '';
    const formattedStartDate = dayjs(startDate).format('DD-MM-YYYY');
    if (clinicalStatus === 'inactive') {
      return `${formattedStartDate} - ${endDate ? dayjs(endDate).format('DD-MM-YYYY') : 'Inactive'}`;
    }
    return `${formattedStartDate} - Present`;
  };

  const memoizedCondition = useMemo(() => selectedCondition, [selectedCondition]);
  const memoizedFamilyMember = useMemo(() => relatedPerson, [relatedPerson]);
  const renderConditionList = (conditionList: any[], title: string) => {
    if (conditionList?.length === 0) {
      return <EmptyStateTabsCard title={`Update ${title}`} />;
    }
    return conditionList?.map((condition: any, index: number) => {
      const evidenceExtensions =
        condition.extension?.filter((ext: { url: string }) => ext.url === HL7_FAMILY_MEMBER_CONDITION_EVIDENCE) || [];
      const indexCode = condition?.code?.coding?.[0]?.code;
      const getIndex = () => {
        return memoizedFamilyMember?.condition?.findIndex(
          (RelatedPersonConditions: RelatedPersonCondition) =>
            RelatedPersonConditions.code?.coding?.[0]?.code === indexCode
        );
      };
      return (
        <ConditionCard
          key={condition.id || index}
          title={condition?.code?.coding?.[0]?.display}
          notes={condition?.note?.[0]?.text}
          diagnosedDate={formatDiagnosedDate(condition)}
          onEdit={() => onEditHandler(condition)}
          onRemove={() => onRemoveHandler(memoizedFamilyMember, indexCode && getIndex())}
          isPublicMode={isPublicMode}
          isCustomEntry={condition.code.coding[0].code === `cn:${PATIENT_DEFINED}`}
        >
          {evidenceExtensions.length > 0 && (
            <Flex
              direction="column"
              gap="2px"
            >
              <LinkedDocumentsLabel />
              <LinkedDocumentsCard records={useExtractDocumentResource(evidenceExtensions)} />
            </Flex>
          )}
        </ConditionCard>
      );
    });
  };
  return (
    <>
      <ModalProvider {...conditionModal}>
        <Modal
          variant={MODAL_VARIANTS.PERIWINKLE}
          title={selectedCondition ? 'Conditions' : 'Conditions'}
          primaryButtonLabel={selectedCondition ? 'Save changes' : 'Add'}
          primaryButtonEnabled={false}
          showSecondaryButton={false}
          isCentered
          {...conditionModal.modalProps}
          {...conditionModal.modalDisclosure}
        >
          <Suspense fallback={<FormSkeleton />}>
            <RelatedPersonConditionForm
              condition={memoizedCondition}
              conditionOptions={conditionOptions}
              conditionTypeOptions={chronicityValue?.compose?.include?.[0]?.concept}
              familyMemberInfo={memoizedFamilyMember!}
            />
          </Suspense>
        </Modal>
      </ModalProvider>
      <Container
        position="relative"
        height="full"
        overflowY="scroll"
        overflowX="hidden"
        className="hide-scrollbar"
      >
        <Flex
          position="fixed"
          top="0"
          right="calc(100% + 12px)"
          backgroundColor="var(--Periwinkle-50, #F8F8FF)"
          borderRadius="lg"
          p="4"
          width="202px"
          textAlign="center"
          display={{ base: 'none', md: 'block' }}
        >
          <Stack>
            {!isPublicMode && (
              <Heading
                color="fluentHealthSecondary.100"
                fontSize="xl"
                fontWeight="400"
              >
                {relatedPerson?.relationship?.coding[0]?.display}
              </Heading>
            )}
          </Stack>
        </Flex>
        <Stack
          py="4"
          width={{ base: 'auto', md: '400px' }}
          height="full"
        >
          <Flex
            justifyContent="space-between"
            fontSize="2xl"
            fontWeight="400"
          >
            <Text>Conditions</Text>
            <SidebarCloseButton onClick={onClose} />
          </Flex>
          {answerList.length === 0 && (
            <SidebarEmptyState
              actionButtonText="Add"
              title="You haven't added any Conditions yet"
              imageSrc="/empty-card-condition.png"
              {...(isPublicMode ? { hideActionButton: true } : { onClick: onAddHandler })}
            />
          )}
          {answerList.length > 0 && (
            <>
              {!isPublicMode && <SidebarAddButton onClick={onAddHandler}>Add</SidebarAddButton>}
              <RoundedTabs>
                <RoundedTabList borderColor="fluentHealthSecondary.400">
                  <RoundedTab>Chronic</RoundedTab>
                  <RoundedTab>Acute</RoundedTab>
                </RoundedTabList>
                <RoundedTabPanels mt={4}>
                  <RoundedTabPanel>{renderConditionList(chronicConditionList, 'chronic conditions')}</RoundedTabPanel>
                  <RoundedTabPanel>{renderConditionList(acuteConditionList, 'acute conditions')}</RoundedTabPanel>
                </RoundedTabPanels>
              </RoundedTabs>
            </>
          )}
          <Spacer />
          <SidebarHelperTooltip
            text="What is a condition?"
            tooltipText="Often synonymous with 'health condition', condition is a broad term that refers to a person's state of health that generally requires care and/or treatment. It may refer to a normal state with regard to one's health, such as pregnancy, or to an abnormal state due to disease, disorder, illness, or injury. It presents with symptoms, and is not a passing illness or injury. Listing medical conditions that you or your family members may be dealing with—including parents, siblings, grandparents, aunts/uncles—can help doctors provide you with better care. Knowing conditions that are common in your family can also help you take proactive steps toward your health by allowing you to recognise potential risks early on.Source: Ministry of Health and Family Welfare"
          />
        </Stack>
      </Container>
    </>
  );
}
