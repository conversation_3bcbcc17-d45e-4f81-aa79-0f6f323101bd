import { useEffect, useState } from 'react';
import {
  Alert,
  AlertDescription,
  Box,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Grid,
  Input,
  InputGroup,
  InputLeftElement,
  Text,
  useTheme,
  useToast,
} from '@chakra-ui/react';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { recordProfileEvents } from '@user/lib/events-analytics-manager';
import { sanitizePhoneNumber } from '@utils/utils';

import { Patient, PatientTelecom } from '@lib/models/patient';
import { IModal, useModalContext } from '../../../../../components/Modal';
import { useAnalyticsService } from '@lib/state';
import { handleKeyPressNumbers, parsePatientName } from '@lib/utils/utils';

import { ReactComponent as SecurityIcon } from '@assets/icons/security-red.svg';

export function GeneralInfoForm({
  patient,
  updatePatient,
}: {
  patient: Patient;
  updatePatient: (payload: any) => void;
}) {
  const [isLoading] = useState<boolean>(false);
  const [setError] = useState<any>(null);
  const [formChanged, setFormChanged] = useState<boolean>(false);
  const toast = useToast();
  const theme = useTheme();
  const { modalDisclosure, setModalProps } = useModalContext();
  const { trackEventInFlow } = useAnalyticsService();

  const initialAlternatePhone =
    patient?.telecom?.find((t: PatientTelecom) => t.system === 'phone' && t.use === 'home')?.value || '';

  const initialAlternateEmail =
    patient?.telecom?.find((t: PatientTelecom) => t.system === 'email' && t.use === 'home')?.value || '';

  const patientPhone = sanitizePhoneNumber(
    patient?.telecom?.find((telecom: PatientTelecom) => telecom.system === 'phone' && telecom.use === 'mobile')?.value
  );

  const patientEmail =
    patient?.telecom?.find((telecom: PatientTelecom) => telecom.system === 'email' && telecom.use === 'mobile')
      ?.value || '';

  const patientBirthday = patient?.birthDate || '';
  const patientName = parsePatientName(patient?.name);
  const [firstName = '', lastName = ''] = patientName.split(' ');

  const form = useForm({
    defaultValues: {
      alternatePhoneNumber: initialAlternatePhone,
      alternateEmailAddress: initialAlternateEmail,
    },
    resolver: zodResolver(
      z.object({
        alternatePhoneNumber: z
          .string()
          .optional()
          .refine((val) => !val || /^\d{10}$/.test(val), {
            message: 'Mobile number must be exactly 10 digits',
          }),
        alternateEmailAddress: z
          .string()
          .optional()
          .refine((val) => !val || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), {
            message: 'Please enter a valid email address',
          }),
      })
    ),
  });
  const {
    handleSubmit,
    formState: { isSubmitting, isValid, errors },
    control,
    register,
    setError: setFormError,
  } = form;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (name === 'alternatePhoneNumber') {
      setFormChanged(value !== initialAlternatePhone);
    } else if (name === 'alternateEmailAddress') {
      setFormChanged(value !== initialAlternateEmail);
    }
  };

  async function onSubmit(data: any) {
    try {
      const payload = {
        path: '/telecom',
        value: [
          {
            use: 'mobile',
            system: 'phone',
            value: patientPhone,
          },
          {
            use: 'mobile',
            system: 'email',
            value: patientEmail,
          },
          {
            use: 'home',
            system: 'phone',
            value: data.alternatePhoneNumber,
          },
          {
            use: 'home',
            system: 'email',
            value: data.alternateEmailAddress,
          },
        ],
      };
      updatePatient(payload);
      recordProfileEvents(trackEventInFlow, {
        EventName: 'GeneralInfoEdited',
        pi_edited_fields: Object.entries(data)
          .map(([k, v]) => `${k}: ${v}`)
          .join(', ')
          .toString(),
      });
      toast({
        title: 'Successfully edited the general info',
        status: 'success',
        duration: 4000,
        isClosable: true,
      });
      modalDisclosure.onClose();
    } catch (err) {
      setError(err);
    }
  }
  function onFormError(data: any) {
    if (data.alternatePhoneNumber) {
      setFormError('alternatePhoneNumber', { message: 'The phone number is invalid.' });
    }
    if (data.alternateEmailAddress) {
      setFormError('alternateEmailAddress', { message: 'Please enter a valid email address.' });
    }
  }

  useEffect(() => {
    setModalProps((prevState: IModal) => ({
      ...prevState,
      primaryButtonEnabled: formChanged && isValid,
      isPrimaryButtonLoading: isSubmitting || isLoading,
      onPrimaryButtonClick: handleSubmit(onSubmit, onFormError),
    }));
  }, [formChanged, isValid, isSubmitting, isLoading, handleSubmit]);

  // Used in formatting error messages

  function onFluentSupport() {
    modalDisclosure.onClose();
  }
  return (
    <Flex direction="column">
      <Alert
        variant="warning"
        mb="32px"
        backgroundColor={theme.colors.papaya['100']}
        color="salmon.500"
      >
        <SecurityIcon style={{ flexShrink: 0 }} />
        <AlertDescription paddingRight="40px">
          For your security, please contact{' '}
          <Text
            display="inline-block"
            cursor="pointer"
            onClick={onFluentSupport}
            textDecoration="underline"
          >
            Fluent Support
          </Text>{' '}
          to update your locked profile information.
        </AlertDescription>
      </Alert>
      <Box
        color="black"
        display="flex"
        flexDirection="column"
        gap="7"
      >
        <Grid
          templateColumns={{ base: 'repeat(1, 1fr)', md: 'repeat(2, 1fr)' }}
          gap="24px"
          py={{ md: '10px' }}
        >
          <FormControl variant="floating">
            <Input
              placeholder=" "
              id="firstName"
              value={firstName}
              disabled
            />
            <FormLabel>First name</FormLabel>
          </FormControl>

          <FormControl variant="floating">
            <Input
              placeholder=" "
              id="lastName"
              value={lastName}
              disabled
            />
            <FormLabel>Last name</FormLabel>
          </FormControl>
        </Grid>
        <Grid
          templateColumns={{ base: 'repeat(1, 1fr)', md: 'repeat(2, 1fr)' }}
          gap="24px"
          py={{ md: '10px' }}
        >
          <FormControl variant="floating">
            <Input
              color="fluentHealthText.100"
              variant="flushed"
              placeholder="Date of birth"
              focusBorderColor="fluentHealthText.250"
              value={patientBirthday ? new Date(patientBirthday).toLocaleDateString('en-GB') : ''}
              disabled
            />
            <FormLabel>Date of birth (DD/MM/YYYY)</FormLabel>
          </FormControl>
          <FormControl variant="floating">
            <Input
              placeholder=" "
              id="phoneNumber"
              value={patientPhone ? `+91 ${patientPhone}` : ''}
              disabled
              maxLength={10}
            />
            <FormLabel>Mobile number</FormLabel>
          </FormControl>
        </Grid>
        <Grid py={{ md: '10px' }}>
          <FormControl variant="floating">
            <Input
              placeholder=" "
              id="email"
              value={patientEmail}
              disabled
            />
            <FormLabel>Email address</FormLabel>
          </FormControl>
        </Grid>
        <Grid py={{ md: '10px' }}>
          <FormControl
            variant="floatingTel"
            isInvalid={!!errors.alternatePhoneNumber}
          >
            <Controller
              name="alternatePhoneNumber"
              control={control}
              render={({ field }) => (
                <InputGroup className={field.value ? 'has__value' : ''}>
                  <InputLeftElement width="auto">+91</InputLeftElement>
                  <Input
                    style={{ paddingLeft: '28px' }}
                    onKeyDown={handleKeyPressNumbers}
                    type="tel"
                    placeholder=" "
                    value={field.value}
                    maxLength={10}
                    onChange={(e) => {
                      field.onChange(e);
                      handleInputChange(e);
                    }}
                    name="alternatePhoneNumber"
                  />
                  <FormLabel>Alternate mobile number</FormLabel>
                </InputGroup>
              )}
            />
            <FormErrorMessage>{errors.alternatePhoneNumber?.message}</FormErrorMessage>
          </FormControl>
        </Grid>
        <Grid py={{ md: '10px' }}>
          <FormControl
            variant="floating"
            isInvalid={!!errors.alternateEmailAddress}
          >
            <Input
              placeholder=" "
              id="alternateEmailAddress"
              {...register('alternateEmailAddress')}
              onChange={(e) => {
                register('alternateEmailAddress').onChange(e);
                handleInputChange(e);
              }}
            />
            <FormLabel>Alternate email address</FormLabel>
            <FormErrorMessage>{errors.alternateEmailAddress?.message}</FormErrorMessage>
          </FormControl>
        </Grid>
      </Box>
    </Flex>
  );
}
