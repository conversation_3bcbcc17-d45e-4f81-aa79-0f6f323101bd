// Package modules
import { useEffect, useState } from 'react';
import { Card, CardBody, Flex, Heading, IconButton, Text } from '@chakra-ui/react';
import { Edit3 as PenIcon } from 'react-feather';
import { useEmergencyContactList, useValueSet } from '@user/lib/medplum-state';
// Local modules
import { recordEmergencyContactEvents } from '@user/lib/events-analytics-manager';
import { useNavigate, useParams } from 'react-router-dom';
import { NavigationHelper, ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { Modal, ModalProvider, useModal } from '../../../../../components/Modal';
import GenericProfileCard from '../../components/GenericProfileCard';
import { RemoveButtonLabel } from '../../components/RemoveButtonLabel';
import EmergencyContactForm from './EmergencyContactForm';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { parsePatientName, parsePhoneNo, parseRelationship, parseValueSet } from '@lib/utils/utils';
import { RelatedPerson } from 'src/gql/graphql';
import { useIsDesktop } from 'src/components/ui/hooks/device.hook';

const MAX_EMERGENCY_CONTACTS = 3;

function EmergencyContactsCard({
  emergencyContacts,
  onAddButtonClick,
  onEditButtonClick,
  isMaxContactsReached = false,
}: {
  emergencyContacts: RelatedPerson[] | null;
  onAddButtonClick: () => void;
  onEditButtonClick: (value: RelatedPerson) => void;
  isMaxContactsReached: boolean;
}) {
  const { isPublicMode } = usePublicSettings();

  const isDesktop = useIsDesktop();
  const [hoveredElement, setHoveredElement] = useState<string | null>(null);

  const { valueSetList } = useValueSet();
  const OPTIONS = parseValueSet(valueSetList?.fmRelationshipValueSet);

  const handleMouseEnter = (contactId: string) => {
    setHoveredElement(contactId);
  };

  const handleMouseLeave = () => {
    setHoveredElement(null);
  };

  if (!emergencyContacts?.length) {
    return (
      <GenericProfileCard
        title="Emergency Contact"
        info="Add trustworthy people who can be contacted in the event of emergencies. The information you enter is for general reference/storage. We do not provide any services in relation to it."
        actionButtonText="emergency contact"
        actionButton={onAddButtonClick}
      />
    );
  }

  return (
    <GenericProfileCard
      title="Emergency Contact"
      info="Add trustworthy people who can be contacted in the event of emergencies. The information you enter is for general reference/storage. We do not provide any services in relation to it."
      actionButton={onAddButtonClick}
      isMaxLimitReached={isMaxContactsReached}
      maxLimitTitleText="You’ve reached the maximum number of Emergency Contact"
      maxLimitInfoText="To add someone else, you must replace an existing contact."
    >
      <Flex
        flexWrap="wrap"
        rowGap="20px"
        columnGap="20px"
      >
        {emergencyContacts.map((contact: any) => (
          <Card
            bgColor="fluentHealthSecondary.500"
            w="full"
            key={contact.id}
            role="group"
            display="flex"
            flex={{ base: '0 0 100%', md: '1 0 45%' }}
            boxShadow="none"
            borderRadius="8px"
            onMouseEnter={() => handleMouseEnter(contact.id)}
            onMouseLeave={handleMouseLeave}
          >
            <CardBody padding="8px 16px 12px 16px">
              <Flex
                direction="column"
                px="2"
                height={!isPublicMode ? '14' : '8'}
              >
                <Heading
                  fontSize="xl"
                  textColor="periwinkle.700"
                  lineHeight="7"
                  maxW="100%"
                  overflow="hidden"
                  textOverflow="ellipsis"
                >
                  {!isPublicMode
                    ? parsePatientName(contact.name)
                    : OPTIONS.find((item: any) => item.value === parseRelationship(contact.relationship))?.label || '-'}
                </Heading>
                {!isPublicMode && (
                  <Text textColor="iris.300">
                    {OPTIONS.find((item: any) => item.value === parseRelationship(contact.relationship))?.label}
                  </Text>
                )}
              </Flex>

              <Flex
                mt="8"
                px="2"
                justifyContent="space-between"
                alignItems="center"
                height="28px"
              >
                <Text textColor="periwinkle.700">
                  {'+91 '}
                  {parsePhoneNo(contact.telecom)}
                </Text>
                {!isPublicMode && ((isDesktop && hoveredElement === contact.id) || !isDesktop) && (
                  <IconButton
                    aria-label="Edit emergency contact"
                    variant="ghost"
                    size="sm"
                    pos="absolute"
                    right="22px"
                    bottom="18px"
                    color="fluentHealthSecondary.100"
                    alignItems="flex-end"
                    onClick={() => onEditButtonClick(contact)}
                    id={contact.id}
                    icon={<PenIcon size={16} />}
                  />
                )}
              </Flex>
            </CardBody>
          </Card>
        ))}
      </Flex>
    </GenericProfileCard>
  );
}

export function EmergencyContactsSection() {
  const navigate = useNavigate();
  const params = useParams();
  const { ehrId, action } = params as any;
  const { PROFILE, EHR, EMERGENCY_CONTACTS } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;
  const [currentContact, setCurrentContact] = useState<RelatedPerson | null>(null);
  const emergencyContactModal = useModal();

  const { isPublicMode } = usePublicSettings();

  const { trackEventInFlow } = useAnalyticsService();
  const { authenticatedUser } = useAuthService();
  const { emergencyContactList } = useEmergencyContactList(authenticatedUser?.id);

  const isMaxContactsReached = emergencyContactList.length >= MAX_EMERGENCY_CONTACTS;

  const onAddButtonClick = () => {
    if (isMaxContactsReached) {
      return;
    }
    setCurrentContact(null);
    recordEmergencyContactEvents(trackEventInFlow, {
      EventName: 'EmergencyContactAddStarted',
    });
    if (NavigationHelper.isDesktop()) {
      navigate(`/${PROFILE}/${EHR}/${EMERGENCY_CONTACTS}/${ADD}`);
    } else {
      emergencyContactModal.modalDisclosure.onOpen();
    }
  };

  const onEditButtonClick = (contact: RelatedPerson) => {
    setCurrentContact(contact);
    emergencyContactModal.modalDisclosure.onOpen();
    recordEmergencyContactEvents(trackEventInFlow, {
      EventName: 'EmergencyContactEditStarted',
    });
  };

  if (isPublicMode && emergencyContactList.length === 0) return null;

  useEffect(() => {
    if (action) {
      if (action === ADD && ehrId === EMERGENCY_CONTACTS) {
        emergencyContactModal.modalDisclosure.onOpen();
      }
    }
  }, [action]);

  return (
    <>
      <EmergencyContactsCard
        emergencyContacts={emergencyContactList}
        onAddButtonClick={onAddButtonClick}
        onEditButtonClick={onEditButtonClick}
        isMaxContactsReached={isMaxContactsReached}
      />
      <ModalProvider {...emergencyContactModal}>
        <Modal
          title="Emergency Contact"
          primaryButtonLabel={currentContact ? 'Save' : 'Add'}
          showSecondaryButton={false}
          tertiaryButtonLabel={<RemoveButtonLabel />}
          tertiaryButtonVariant="quiet"
          showTertiaryButton={!!currentContact}
          isCentered
          {...emergencyContactModal.modalProps}
          {...emergencyContactModal.modalDisclosure}
          onClose={() => {
            emergencyContactModal.modalDisclosure.onClose();
            setCurrentContact(null);
            navigate(NavigationHelper.getBasicsView(true, { absolutePath: true }));
          }}
        >
          <EmergencyContactForm contact={currentContact} />
        </Modal>
      </ModalProvider>
    </>
  );
}
