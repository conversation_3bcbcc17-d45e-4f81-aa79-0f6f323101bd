// Package modules
import React, { useCallback, useEffect, useState } from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod/dist/zod';
import { z } from 'zod';
import {
  Box,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  InputGroup,
  InputLeftElement,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { RelatedPerson } from '@gql/graphql';
// Local modules
import { EmergencyContactPayLoad } from '@user/lib/models/emergency-contact';
import { useEmergencyContactList, useValueSet } from '@user/lib/medplum-state';
import { cleanPhoneNumberRegex } from '@utils/regex';
import { EmergencyContactFormValues } from '@src/types/forms';
import { recordEmergencyContactEvents } from '@user/lib/events-analytics-manager';
import { NavigationHelper } from '@user/lib/constants';
import { useNavigate } from 'react-router-dom';

import { useModalContext } from '../../../../../components/Modal';
import { Select, SelectOptionProps } from '../../../../../components/ui/Select';
import {
  cleanPhoneNo,
  handleKeyPressNumbers,
  parsePatientName,
  parsePhoneNo,
  // parseRelation,
  parseValueSet,
} from '@lib/utils/utils';
import { useAnalyticsService, useAuthService } from '@lib/state';
import {
  ConsentModal,
  ConsentModalContent,
  ConsentModalFooter,
  ConsentModalHeading,
  ConsentModalPrimaryButton,
  ConsentModalSecondaryButton,
} from '../../components/ConsentModal';

// Helpers
const getInitialFormData = (contact: RelatedPerson | null) => {
  const name = parsePatientName(contact?.name, '');
  const [firstName = '', lastName = ''] = name.split(' ');
  const relation = contact?.relationship?.find((item: any) => {
    return item.coding?.some((coding: any) => coding.code !== 'C');
  });
  const coding = relation?.coding?.[0];
  const phoneNumber = cleanPhoneNo(parsePhoneNo(contact?.telecom));

  return {
    firstName,
    lastName,
    phoneNumber,
    relation: {
      value: coding?.code || '',
      label: coding?.display || '',
    },
  };
};

export default function EmergencyContactForm({ contact }: { contact: RelatedPerson | null }) {
  // Add/edit form type
  const isEditForm = contact !== null;

  const { authenticatedUser: patient } = useAuthService();
  const { addContact, updateContact, deleteContact } = useEmergencyContactList(patient.id);
  const { trackEventInFlow } = useAnalyticsService();

  const toast = useToast();
  const { modalDisclosure, setModalProps } = useModalContext();
  const navigate = useNavigate();
  const removeContactModal = useDisclosure();

  const [, setError] = useState<any>(null);
  const { valueSetList } = useValueSet();
  const OPTIONS = parseValueSet(valueSetList?.fmRelationshipValueSet);

  const form = useForm({
    resolver: zodResolver(
      z.object({
        firstName: z.string().min(1).max(50),
        lastName: z.string().min(1).max(50),
        relation: z.object({
          value: z.string().optional(),
          label: z.string().optional(),
        }),
        phoneNumber: z.string().regex(cleanPhoneNumberRegex, 'Phone number is invalid'),
      })
    ),
    defaultValues: getInitialFormData(contact),
  });
  const {
    handleSubmit,
    control,
    formState: { isSubmitting, isValid },
    reset,
    register,
    setError: setFormError,
  } = form;

  const [isLoading, setIsLoading] = useState<boolean>(false); // Used for contact remove -- cannot bind to form's isLoading
  const relationField = form.watch('relation');

  const handleKeyPressLetters = (e: React.KeyboardEvent) => {
    if (!/[A-Za-z]/.test(e.key)) {
      e.preventDefault();
    }
  };

  async function onRemove() {
    try {
      setIsLoading(true);

      removeContactModal.onOpen();
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }

  const customRelationSelect = useCallback((value: SelectOptionProps | any) => {
    form.setValue('relation', { value: value?.value, label: value?.label });
    form.trigger('relation');
    if (!contact) {
      recordEmergencyContactEvents(trackEventInFlow, {
        EventName: 'EmergencyContactInProgRelationship',
        ec_relationship: value?.value,
      });
    }
  }, []);

  async function onSubmit(data: EmergencyContactFormValues) {
    try {
      const payload: EmergencyContactPayLoad = {
        firstName: data.firstName,
        lastName: data.lastName,
        phoneNumber: data.phoneNumber,
      };
      if (data?.relation) {
        const options = OPTIONS.find((item: any) => item.value === data?.relation?.value);
        if (options) {
          const { value, label } = options;
          payload.relationLabel = label;
          payload.relationCode = value;
        }
      }
      if (isEditForm) {
        await updateContact({ contactId: contact!.id, payload });
      } else {
        await addContact(payload);
      }
      recordEmergencyContactEvents(trackEventInFlow, {
        EventName: isEditForm ? 'EmergencyContactEditCompleted' : 'EmergencyContactCompleted',
        ec_first_name: data?.firstName,
        ec_last_name: data?.lastName,
        ec_mobile_number: data?.phoneNumber,
        ec_relationship: data?.relation?.value,
      });
      toast({
        title: `Successfully ${isEditForm ? 'edit' : 'add'}ed the emergency contact`,
        status: 'success',
        duration: 4000,
        isClosable: true,
      });

      // Clear form if "add" mode
      if (!isEditForm) reset();

      modalDisclosure.onClose();
      navigate(NavigationHelper.getBasicsView(true, { absolutePath: true }));
    } catch (err) {
      setError(err);
    }
  }

  // Used in formatting error messages
  function onFormError(data: any) {
    if (data.firstName) {
      setFormError('firstName', { message: 'First name cannot be empty.' });
    }
    if (data.lastName) {
      setFormError('lastName', { message: 'Last name cannot be empty.' });
    }
    if (data.phoneNumber) {
      setFormError('phoneNumber', { message: 'The phone number is invalid.' });
    }
  }

  async function onRemoveEmergencyContact() {
    try {
      setIsLoading(true);
      // TODO - handle the case when there is no contact id
      if (!contact?.id) return;
      await deleteContact(contact!.id);

      toast({
        title: `Successfully removed the emergency contact`,
        status: 'success',
        duration: 4000,
        isClosable: true,
      });
      recordEmergencyContactEvents(trackEventInFlow, {
        EventName: 'EmergencyContactRemoved',
      });
      removeContactModal.onClose();
      modalDisclosure.onClose();
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    setModalProps((prevState) => ({
      ...prevState,
      primaryButtonEnabled: isValid,
      isPrimaryButtonLoading: isSubmitting || isLoading,
      onPrimaryButtonClick: handleSubmit(onSubmit, onFormError),
      onTertiaryButtonClick: onRemove,
    }));
  }, [isEditForm, isValid, isSubmitting, isLoading, handleSubmit]);

  return (
    <FormProvider {...form}>
      <ConsentModal {...removeContactModal}>
        <ConsentModalHeading>Are you sure you want to remove this entry?</ConsentModalHeading>
        <ConsentModalContent>This cannot be undone.</ConsentModalContent>
        <ConsentModalFooter>
          <ConsentModalSecondaryButton
            variant="quietDanger"
            color="red.100"
            isLoading={isLoading}
            onClick={onRemoveEmergencyContact}
          >
            Remove
          </ConsentModalSecondaryButton>
          <ConsentModalPrimaryButton onClick={removeContactModal.onClose}>Cancel</ConsentModalPrimaryButton>
        </ConsentModalFooter>
      </ConsentModal>
      <Flex direction="column">
        {/* Content / form */}
        <Box
          color="black"
          display="flex"
          flexDirection="column"
          gap="7"
          py="4"
        >
          <Flex gap="24px">
            <FormControl variant="floating">
              <Input
                onKeyDown={handleKeyPressLetters}
                type="text"
                placeholder=" "
                id="firstName"
                {...register('firstName', { minLength: 1 })}
                onBlurCapture={(e) => {
                  if (!contact) {
                    recordEmergencyContactEvents(trackEventInFlow, {
                      EventName: 'EmergencyContactInProgFirstName',
                      ec_first_name: e.target.value,
                    });
                  }
                }}
              />
              <FormLabel>First name*</FormLabel>
              <FormErrorMessage>First name is required</FormErrorMessage>
            </FormControl>

            <FormControl variant="floating">
              <Input
                onKeyDown={handleKeyPressLetters}
                type="text"
                placeholder=" "
                id="lastName"
                {...register('lastName', { minLength: 1 })}
                onBlurCapture={(e) => {
                  if (!contact) {
                    recordEmergencyContactEvents(trackEventInFlow, {
                      EventName: 'EmergencyContactInProgLastName',
                      ec_last_name: e.target.value,
                    });
                  }
                }}
              />
              <FormLabel>Last name*</FormLabel>
              <FormErrorMessage>Last name is required</FormErrorMessage>
            </FormControl>
          </Flex>
          <Select
            labelText="Relationship"
            value={OPTIONS.find((item: any) => item.label === relationField?.label)}
            onChange={customRelationSelect}
            options={OPTIONS}
            menuPosition="fixed"
            isSearchable={false}
            isClearable
          />

          <FormControl variant="floatingTel">
            <Controller
              name="phoneNumber"
              control={control}
              render={({ field }) => (
                <InputGroup className={field.value ? 'has__value' : ''}>
                  <InputLeftElement width="auto">+91</InputLeftElement>
                  <Input
                    style={{ paddingLeft: '28px' }}
                    onKeyDown={handleKeyPressNumbers}
                    type="tel"
                    placeholder=" "
                    value={field.value}
                    maxLength={10}
                    {...register('phoneNumber', { minLength: 6, maxLength: 10 })}
                    onBlurCapture={(e) => {
                      if (!contact) {
                        recordEmergencyContactEvents(trackEventInFlow, {
                          EventName: 'EmergencyContactInProgMobileNumber',
                          ec_mobile_number: e.target.value,
                        });
                      }
                    }}
                  />
                  <FormLabel>Contact number*</FormLabel>
                  <FormErrorMessage>Contact number is required</FormErrorMessage>
                </InputGroup>
              )}
            />
          </FormControl>
        </Box>
      </Flex>
    </FormProvider>
  );
}
