import { Suspense } from 'react';
import { Flex, Text } from '@chakra-ui/react';

import { ProfileCardSkeleton } from '../components/ProfileCardSkeleton';
import { GeneralInfoSection } from './components/GeneralInfoCard';
import { EmergencyContactsSection } from './components/EmergencyContactsSection';
import { HealthInsuranceSection } from './components/HealthInsuranceSection';
import { HealthCareProxySection } from './components/HealthcareProxySection';
import { usePublicSettings } from '@lib/state';

export function BasicInfoPage() {
  const { isPublicMode, myConsent } = usePublicSettings();
  const isAdditionalDetailsVisible =
    !isPublicMode ||
    myConsent.some((val: any) => ['emergencyContacts', 'healthcareProxy', 'healthInsurance'].includes(val.meaning));
  const isEmergencyContactsAvailable =
    !isPublicMode || myConsent.find((val: any) => val.meaning === 'emergencyContacts');
  const isHealthcareProxyAvailable = !isPublicMode || myConsent.find((val: any) => val.meaning === 'healthcareProxy');
  const isHealthInsuranceAvailable = !isPublicMode || myConsent.find((val: any) => val.meaning === 'healthInsurance');

  return (
    <Flex
      direction="column"
      gap="20px"
    >
      <Suspense fallback={<ProfileCardSkeleton title="General Info" />}>
        <GeneralInfoSection />
      </Suspense>
      {isAdditionalDetailsVisible && (
        <Flex
          gap="4px"
          margin="0px 16px"
          direction="column"
        >
          <Text
            textTransform="uppercase"
            fontSize="13"
            fontWeight="medium"
            letterSpacing="1.56px"
            lineHeight="16px"
          >
            ADDITIONAL DETAILS
          </Text>
          {!isPublicMode && (
            <Text
              fontSize="16"
              fontWeight="normal"
              letterSpacing="-0.32px"
              lineHeight="24px"
              color="fluentHealthText.200"
            >
              The information you enter is for general reference/storage. We do not provide any services in relation to
              it.
            </Text>
          )}
        </Flex>
      )}
      {isEmergencyContactsAvailable && (
        <Suspense fallback={<ProfileCardSkeleton title="Emergency Contacts" />}>
          <EmergencyContactsSection />
        </Suspense>
      )}
      {isHealthcareProxyAvailable && (
        <Suspense fallback={<ProfileCardSkeleton title="Alternative Medical Decision-Maker" />}>
          <HealthCareProxySection />
        </Suspense>
      )}
      {isHealthInsuranceAvailable && (
        <Suspense fallback={<ProfileCardSkeleton title="Health Insurance" />}>
          <HealthInsuranceSection />
        </Suspense>
      )}
    </Flex>
  );
}
