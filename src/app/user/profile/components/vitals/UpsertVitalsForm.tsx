import {
  Box,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  InputGroup,
  InputRightElement,
  useDisclosure,
  useTheme,
} from '@chakra-ui/react';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { Clock as ClockIcon } from 'react-feather';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { VITALS } from '@user/lib/constants';
import { VitalsEventProps, recordVitalsEvents } from '@user/lib/events-analytics-manager';

import { IModal } from 'src/components/Modal';
import { DatePickerField, SuggestionDropdown } from 'src/components/ui/Form';
import { TimeOfRecordingOption } from '@lib/models/misc';
import { HOURLY_BASE_OPTIONS } from '@lib/constants';
import { useAnalyticsService } from '@lib/state';

const VALIDATION = {
  required: true,
  minLength: 1,
  maxLength: 40,
  validate: (value: string) => !!value.trim(),
};
const EventField = (mainFieldName: string) => {
  switch (mainFieldName) {
    case VITALS.BodyTemperature.key:
      return {
        PrimaryEvent: 'VTBTAddInProgTemparature',
        DateEvent: 'VTBTAddInProgDate',
        TimeEvent: 'VTBTAddInProgTime',
        PrimaryKey: 'vt_bt_temparature',
        DateKey: 'vt_bt_date',
        TimeKey: 'vt_bt_time',
      };
    case VITALS.PulseRate.key:
      return {
        PrimaryKey: 'vt_pr_rate',
        DateEvent: 'VTPRAddInProgDate',
        TimeEvent: 'VTPRAddInProgTime',
        PrimaryEvent: 'VTPRAddInProgRate',
        DateKey: 'vt_pr_date',
        TimeKey: 'vt_pr_time',
      };
    case VITALS.OxygenSaturationLevel.key:
      return {
        PrimaryEvent: 'VTOSLAddInProgLevel',
        DateEvent: 'VTOSLAddInProgDate',
        TimeEvent: 'VTOSLAddInProgTime',
        PrimaryKey: 'vt_osl_level',
        DateKey: 'vt_osl_date',
        TimeKey: 'vt_osl_time',
      };
    case VITALS.RespiratoryRate.key:
      return {
        PrimaryEvent: 'VTRRAddInProgRate',
        DateEvent: 'VTRRAddInProgDate',
        TimeEvent: 'VTRRAddInProgTime',
        PrimaryKey: 'vt_rr_rate',
        DateKey: 'vt_rr_date',
        TimeKey: 'vt_rr_time',
      };
    default:
      return {};
  }
};
export const TIME_OF_RECORDING_BASE_OPTIONS = HOURLY_BASE_OPTIONS.map((el) => ({
  ...el,
  hideIcon: true,
})) as TimeOfRecordingOption[];

interface IUpsertVitalsModal {
  mainFieldName: string;
  mainFieldLabel: string;
  selectedVital: any;
  myFormState: 'Add' | 'Edit';
  onClose?: () => void;
  onSubmit?: (formValues: any) => void;
  setModalState: (modalState: IModal) => void;
  minReqValue?: number;
  maxReqValue?: number;
}

const getDefaultValues = (mainFieldName: string, selectedVital: any) => {
  const vitalCode = Object.values(VITALS).find((vital) => vital.key === mainFieldName)?.code;
  const isCurrentVital = selectedVital?.code?.coding?.some((coding: { code?: string }) => coding?.code === vitalCode);
  const value = isCurrentVital ? selectedVital?.valueQuantity?.value : undefined;
  const effectiveDateTime = isCurrentVital ? selectedVital?.effectiveDateTime : null;
  const dateOfRecording = effectiveDateTime ? dayjs(effectiveDateTime).format('YYYY-MM-DD') : null;
  const timeOfRecording = effectiveDateTime ? dayjs(effectiveDateTime).format('HH:mm:ss') : null;

  return {
    [mainFieldName]: value,
    dateOfRecording,
    timeOfRecording: timeOfRecording ? dayjs(timeOfRecording, 'HH:mm:ss').format('HH:mm') : '',
  };
};

export function UpsertVitalsForm({
  selectedVital,
  mainFieldName,
  mainFieldLabel,
  onSubmit,
  setModalState,
  minReqValue,
  maxReqValue,
}: IUpsertVitalsModal) {
  const datePickerPopover = useDisclosure();
  const theme = useTheme();
  const { trackEventInFlow } = useAnalyticsService();

  const [timeOfRecordingOptions, setTimeOfRecordingOptions] = useState(TIME_OF_RECORDING_BASE_OPTIONS);
  const defaultValue = getDefaultValues(mainFieldName, selectedVital);
  const form: any = useForm({
    mode: 'onChange',
    defaultValues: defaultValue,
    resolver: zodResolver(
      z.object({
        [mainFieldName]: z.string().refine(
          (val) => {
            const num = parseFloat(val);
            if (Number.isNaN(num)) return false;
            if (mainFieldName === 'bodyTemperature' && num >= 30 && num <= 112) {
              return !(num >= 46 && num <= 85);
            }
            return (
              (minReqValue === undefined || num >= minReqValue) && (maxReqValue === undefined || num <= maxReqValue)
            );
          },
          {
            message:
              minReqValue !== undefined && maxReqValue !== undefined
                ? `Please input valid data`
                : // Future Todo  `${mainFieldLabel} value ${minReqValue ? `must be at least ${minReqValue}` : (maxReqValue ? `but the maximum allowed is ${maxReqValue}` : "is required")}.`
                  `${mainFieldLabel} is required.`,
          }
        ),

        dateOfRecording: z.string().min(1),
        timeOfRecording: z.string().regex(/^(?:2[0-3]|[01][0-9]):[0-5][0-9]$/, 'Format should be hh:mm'),
      })
    ),
  });
  const {
    handleSubmit,
    register,
    formState: { isSubmitting, isValid },
  } = form;
  const dateField = form.watch('dateOfRecording');
  const timeField = form.watch('timeOfRecording');

  const datePickerChangeHandler = (date: Date | null) => {
    if (dayjs(date).isValid()) {
      form.setValue('dateOfRecording', dayjs(date).format('YYYY-MM-DD'));
    } else {
      form.setValue('dateOfRecording', '');
    }
    if (!selectedVital) {
      recordVitalsEvents(trackEventInFlow, {
        EventName: EventField(mainFieldName).DateEvent as VitalsEventProps['EventName'],
        [EventField(mainFieldName).DateKey as string]: dayjs(date).format('YYYY/MM/DD'),
      });
    }
    datePickerPopover.onClose();
  };

  const datePickerClearHandler = () => {
    form.setValue('dateOfRecording', '');
    datePickerPopover.onClose();
  };

  const onTimeOfRecordingSelect = useCallback((value: TimeOfRecordingOption) => {
    form.setValue('timeOfRecording', value.value);
    form.trigger('timeOfRecording');
    if (!selectedVital) {
      recordVitalsEvents(trackEventInFlow, {
        EventName: EventField(mainFieldName).TimeEvent as VitalsEventProps['EventName'],
        [EventField(mainFieldName).TimeKey as string]: value?.value,
      });
    }
  }, []);

  const onTimeOfRecordingSearchChange = useCallback(
    async (searchText: string) => {
      const newList = TIME_OF_RECORDING_BASE_OPTIONS.filter((option) => option.value.includes(searchText));
      setTimeOfRecordingOptions(newList);
      return newList;
    },
    [form]
  );

  const formSubmitHandler = async (values: any) => {
    let value = values;
    if (selectedVital) {
      value = Object.assign(selectedVital, values);
    }
    await onSubmit?.(value);
    form.reset();
  };

  useEffect(() => {
    setModalState({
      onPrimaryButtonClick: handleSubmit(formSubmitHandler),
      primaryButtonEnabled: isValid,
      isPrimaryButtonLoading: isSubmitting,
    });
  }, [isValid, isSubmitting]);

  return (
    <FormProvider {...form}>
      <Box
        color="black"
        display="flex"
        flexDirection="column"
        mt="24px"
        gap="14"
        mx="2"
      >
        <FormControl
          variant="floating"
          isInvalid={!!form.formState.errors[mainFieldName]}
        >
          <Input
            placeholder=" "
            type="number"
            onBlurCapture={(e) => {
              if (!Object.keys(form.formState.errors).length) {
                setModalState({
                  onPrimaryButtonClick: handleSubmit(formSubmitHandler),
                  primaryButtonEnabled: true,
                  isPrimaryButtonLoading: isSubmitting,
                });
              }
              if (!form.formState.errors[mainFieldName] && !selectedVital) {
                recordVitalsEvents(trackEventInFlow, {
                  EventName: EventField(mainFieldName).PrimaryEvent as VitalsEventProps['EventName'],
                  [EventField(mainFieldName).PrimaryKey as string]: e.target.value,
                });
              }
            }}
            {...register(mainFieldName, VALIDATION)}
          />
          <FormLabel>{mainFieldLabel}</FormLabel>
          <FormErrorMessage>
            {form?.formState?.errors?.[mainFieldName]?.message ?? 'This field is required'}
          </FormErrorMessage>
        </FormControl>
        <Flex
          justifyContent="space-between"
          gap="6"
          mb="10"
        >
          <DatePickerField
            // Field props
            name="dateOfRecording"
            labelText="Date*"
            errorText="This field is required"
            rules={{ required: true }}
            isInvalid={
              form.formState.touchedFields.dateOfRecording && form.control._formValues?.dateOfRecording?.length === 0
            }
            // Datepicker props
            datePickerChangeHandler={datePickerChangeHandler}
            datePickerClearHandler={datePickerClearHandler}
            datePickerPopover={datePickerPopover}
            isClearDateButtonDisabled={dateField?.length === 0}
            selected={dayjs(dateField).isValid() ? dayjs(dateField).toDate() : null}
            maxDate={new Date()}
          />
          <Box
            width="100%"
            position="relative"
          >
            <FormControl
              variant="floating"
              isInvalid={!!form.formState.errors.timeOfRecording}
            >
              <SuggestionDropdown
                options={timeOfRecordingOptions}
                textValue={timeField}
                onSelect={onTimeOfRecordingSelect}
                onChange={onTimeOfRecordingSearchChange}
                onClear={() => {}}
                keepOpenAfterBlur={false}
                resetTextValueAfterBlur={false}
                debounceDelay={100}
                isFreeInput
              >
                {({ searchInputChangeHandler, searchInputBlueHandler, suggestionDropdownPopover }) => (
                  <InputGroup display="block">
                    <Input
                      placeholder=" "
                      {...register('timeOfRecording', {
                        onChange: searchInputChangeHandler,
                        onBlur: searchInputBlueHandler,
                      })}
                    />
                    <FormLabel fontWeight="normal">Time*</FormLabel>
                    <FormErrorMessage>{form.formState.errors.timeOfRecording?.message}</FormErrorMessage>
                    <InputRightElement
                      w="36px"
                      h="36px"
                      cursor="pointer"
                      _hover={{
                        '& ~ input': {
                          borderColor: 'fluentHealth.500',
                        },
                      }}
                      {...{
                        onClick: () => {
                          // Reset the list of filtered options when clicking on the icon button
                          setTimeOfRecordingOptions(TIME_OF_RECORDING_BASE_OPTIONS);
                          suggestionDropdownPopover.onOpen();
                        },
                      }}
                    >
                      <ClockIcon
                        size={18}
                        color={theme.colors.papaya[600]}
                      />
                    </InputRightElement>
                  </InputGroup>
                )}
              </SuggestionDropdown>
            </FormControl>
          </Box>
        </Flex>
      </Box>
    </FormProvider>
  );
}
