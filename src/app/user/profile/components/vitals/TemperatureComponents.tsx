import React, { Suspense, useEffect, useState } from 'react';
import { Flex, Spacer, Stack, Text, useDisclosure, useToast } from '@chakra-ui/react';
import { useMasterObservationResponseList } from '@user/lib/medplum-state';
import { LOINC_ORG, NavigationHelper, ROUTE_ACTIONS, ROUTE_VARIABLES, VITALS } from '@user/lib/constants';
import { ObservationVitalResponsePayload } from '@user/lib/models/questionnaire-response';
import dayjs from 'dayjs';
import { recordVitalsEvents } from '@user/lib/events-analytics-manager';
import { useNavigate } from 'react-router-dom';

import { MasterVital, PatientVitals } from '@lib/models/patient-vitals';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { IModal, MODAL_VARIANTS, Modal } from '../../../../../components/Modal';
import { UpsertVitalsForm } from './UpsertVitalsForm';
import { VitalItemCard } from './VitalSidebarComponents';
import { CardSkeleton, GraphModalButtons, SidebarHelperTooltip } from '../SidebarComponents';
import { SidebarEmptyState } from '../SidebarEmptyState';
import { FHIR_CODE_SYSTEM_URL, deleteIdentifier, toCapitalize } from '@lib/constants';

function TemperatureItemList({
  setSelectedVital,
  answerList,
  upsertModal,
  onTrackEditClickEvent,
}: // searchQuery,
{
  setSelectedVital: React.Dispatch<React.SetStateAction<PatientVitals | null>>;
  upsertModal: any;
  answerList: any;
  onTrackEditClickEvent: () => void;
}) {
  const toast = useToast();
  const { authenticatedUser } = useAuthService();
  const { trackEventInFlow } = useAnalyticsService();
  const { deleteObservationResponseTask } = useMasterObservationResponseList(authenticatedUser?.id);
  const { isPublicMode } = usePublicSettings();
  const onRemoveVital = async (vitals: any) => {
    const deleteTask = 'Delete Vitals';
    const identifier = `${deleteIdentifier}:vitals`;
    const payload: ObservationVitalResponsePayload = {
      observationId: vitals?.id,
      deleteTask,
      identifier,
    };
    try {
      await deleteObservationResponseTask(payload);

      toast({
        title: `${VITALS.BodyTemperature.label} removed`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (e) {
      toast({
        title: 'Error',
        description: `An error occurred while removing the ${VITALS.BodyTemperature.label}`,
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      recordVitalsEvents(trackEventInFlow, {
        EventName: 'VTBTRemoved',
      });
    }
  };

  return answerList.length === 0 ? (
    <Flex
      direction="column"
      gap="12px"
      height="full"
    >
      <SidebarEmptyState
        title="No Temperature recordings found."
        imageSrc="/empty-card-vital-temperature.png"
        hideActionButton
      />
    </Flex>
  ) : (
    <Stack
      overflowY="scroll"
      className="hide-scrollbar"
      gap="2"
    >
      {answerList.map((answer: any) => (
        <VitalItemCard
          key={answer.id}
          vitals={answer}
          vitalDetails={
            <Text
              fontSize="32px"
              color="gray.500"
              fontWeight="500"
            >
              {answer?.valueQuantity?.value ? `${answer.valueQuantity.value}${answer.valueQuantity.unit}` : ''}
            </Text>
          }
          bottomLabel={answer?.effectiveDateTime && `Recorded at ${dayjs(answer.effectiveDateTime).format('HH:mm')}`}
          onRemove={() => onRemoveVital(answer)}
          onEdit={() => {
            setSelectedVital(answer);
            upsertModal.onOpen();
            onTrackEditClickEvent();
          }}
          isPublicMode={isPublicMode}
        />
      ))}
    </Stack>
  );
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function TemperatureSection({ masterVital, action }: { masterVital: MasterVital; action: string }) {
  const { authenticatedUser } = useAuthService();
  const { masterVitalList, addObservationResponse, updateObservationResponse } = useMasterObservationResponseList(
    authenticatedUser?.id
  );
  const { PROFILE, EHR, VITALS: VITALS_ROUTE, BODY_TEMPERATURE } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;
  const answerList = masterVitalList[VITALS.BodyTemperature.key];

  const { isPublicMode } = usePublicSettings();
  const [modalState, setModalState] = useState<IModal>({});
  const [selectedVital, setSelectedVital] = useState<any | null>(null);
  const toast = useToast();
  const navigate = useNavigate();
  const upsertModal = useDisclosure();
  const { trackEventInFlow } = useAnalyticsService();

  const onAddHandler = () => {
    navigate(`/${PROFILE}/${EHR}/${VITALS_ROUTE}/${BODY_TEMPERATURE}/${ADD}`);
    recordVitalsEvents(trackEventInFlow, {
      EventName: 'VTBTAddStarted',
      vt_bt_entry_point: 'my_health_profile',
    });
  };

  const onTrackEditClickEvent = () => {
    recordVitalsEvents(trackEventInFlow, {
      EventName: 'VTBTInteracted',
      vt_bt_entry_point: 'my_health_profile',
    });
  };

  const onSubmit = async (vitals: any) => {
    const { id, dateOfRecording, timeOfRecording } = vitals;
    const bodyTemperature = vitals[VITALS.BodyTemperature.key];
    const effectiveDateTime =
      dateOfRecording && timeOfRecording
        ? dayjs(`${dateOfRecording}T${timeOfRecording}`).format('YYYY-MM-DDTHH:mm:ss')
        : undefined;

    const payload = {
      ...(id && { id }),
      item: VITALS.BodyTemperature.key,
      code: {
        coding: [
          {
            system: FHIR_CODE_SYSTEM_URL,
            code: VITALS.BodyTemperature.key,
            display: VITALS.BodyTemperature.label,
          },
          {
            system: LOINC_ORG,
            code: VITALS.BodyTemperature.code,
            display: VITALS.BodyTemperature.label,
          },
        ],
      },
      recordingDateTime: effectiveDateTime,
      valueQuantity: {
        value: parseFloat(bodyTemperature),
        unit: parseFloat(bodyTemperature) > 45 ? '°F' : '°C',
        system: 'http://unitsofmeasure.org',
        code: parseFloat(bodyTemperature) > 45 ? 'degF' : 'degC',
      },
    };

    try {
      if (id) {
        await updateObservationResponse(payload);
      } else {
        await addObservationResponse(payload);
      }
      toast({
        title: `Successfully ${id ? 'updated' : 'added'} ${VITALS.BodyTemperature.label}`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      upsertModal.onClose();
      navigate(NavigationHelper.getEhrView(false, VITALS_ROUTE, BODY_TEMPERATURE));
    } catch (err) {
      toast({
        title: 'Something went wrong. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      recordVitalsEvents(trackEventInFlow, {
        EventName: id ? 'VTBTEdited' : 'VTBTAddCompleted',
        vt_bt_entry_point: 'my_health_profile',
        vt_bt_temparature: bodyTemperature,
        vt_bt_date: dayjs(dateOfRecording).format('YYYY/MM/DD'),
        vt_bt_time: timeOfRecording,
      });
    }
  };

  useEffect(() => {
    if (!upsertModal.isOpen) setSelectedVital(null);
  }, [upsertModal.isOpen]);

  useEffect(() => {
    if (action === 'add') {
      upsertModal.onOpen();
    }
  }, [action]);

  return (
    <>
      <Modal
        title={toCapitalize(masterVital?.value)}
        primaryButtonLabel={selectedVital ? 'Save' : 'Add'}
        showSecondaryButton={false}
        isCentered
        scrollY="false"
        maxH="1000px"
        variant={MODAL_VARIANTS.PERIWINKLE}
        {...modalState}
        {...upsertModal}
        onClose={() => {
          upsertModal.onClose();
          navigate(NavigationHelper.getEhrView(false, VITALS_ROUTE, BODY_TEMPERATURE));
        }}
      >
        <UpsertVitalsForm
          mainFieldName={VITALS.BodyTemperature.key}
          mainFieldLabel="Temperature*"
          selectedVital={selectedVital}
          onSubmit={onSubmit}
          minReqValue={30}
          maxReqValue={45}
          setModalState={setModalState}
          myFormState={selectedVital ? 'Edit' : 'Add'}
        />
      </Modal>

      {answerList.length === 0 ? (
        <SidebarEmptyState
          title="Update body temperature readings"
          actionButtonText="Add"
          imageSrc="/empty-card-vital-temperature.png"
          {...(isPublicMode ? { hideActionButton: true } : { onClick: onAddHandler })}
          isPublicMode={isPublicMode}
        />
      ) : (
        <Stack
          gap="2"
          height="full"
          width="full"
          overflowY="hidden"
          className="hide-scrollbar"
        >
          <GraphModalButtons {...(isPublicMode ? {} : { onAddClick: onAddHandler })} />
          <Suspense fallback={<CardSkeleton />}>
            <TemperatureItemList
              answerList={answerList}
              setSelectedVital={setSelectedVital}
              upsertModal={upsertModal}
              onTrackEditClickEvent={onTrackEditClickEvent}
            />
          </Suspense>
          <Spacer />
        </Stack>
      )}
      {!isPublicMode && (
        <SidebarHelperTooltip
          text="What is body temperature?"
          tooltipText="Human body temperature is maintained at 97.7–99.5°F (36.5–37.5°C) by balancing heat load, which originates from environmental heat exposure and metabolic processes. According to the Ministry of Health and Family Welfare, a fever is demonstrated when internal heat levels ascend past 100.4°F (38°C). It usually happens while the body deals with an unfamiliar microorganism. The rise in temperature is a reaction to battling the microbe. It obliterates the microorganism with heat and establishes an optimal climate for different cells in a safe response."
        />
      )}
    </>
  );
}
