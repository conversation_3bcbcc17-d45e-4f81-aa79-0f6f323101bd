import { Card, Container, Flex, HStack, Stack, Text, VStack, useTheme } from '@chakra-ui/react';
import { ChevronLeft, ChevronRight } from 'react-feather';
import React, { Suspense, lazy, useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';

import { useAuthService } from '@lib/state';
import {
  PREVENTATIVE_SCREENING,
  PREVENTATIVE_SCREENING_LINK_ID,
  ROUTE_ACTIONS,
  ROUTE_VARIABLES,
  preventativeScreeningConstants,
} from '../../lib/constants';
import { MasterScreening } from '@lib/models/screening';
import { FluentHealthLoader } from '../../../../components/FluentHealthLoader';
import { SidebarCloseButton, SidebarHeading } from './SidebarComponents';
import { useGetPrevenativeScreening } from '../../lib/medplum-state';
import { ISidebarProps } from '@lib/models/misc';

// eslint-disable-next-line @typescript-eslint/naming-convention
const PreventativeScreeningEntryList = lazy(() => import('./preventative-screening/PreventativeScreeningEntryList'));

// Constants
const ACTION_BUTTON_RESOLVER_LIST = {
  [PREVENTATIVE_SCREENING.annual_physical.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSAnnualPhysical}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      emptyStateImageSrc="/empty-card-annual-physical.png"
      infoDescription="According to the WHO, an annual health check-up during which medical experts note down medical history, conduct a physical examination, and advise blood tests depending on age, conditions and findings.  Examination includes checking vital signs like blood pressure, heart (pulse) rate, and temperature, and examining various organs such as the heart and lungs. Recommendations are made by medical experts on appropriate vaccinations, screenings and laboratory tests."
      action={action}
      subActive={subActive}
    />
  ),
  [PREVENTATIVE_SCREENING.eye_examination.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSEyeExamination}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      emptyStateImageSrc="/empty-card-eye-examination.png"
      infoDescription="The WHO explains how eye examinations are used for general assessment, diagnosis and monitoring of a range of eye conditions. Examining doctors will further guide patients based on examination findings."
      action={action}
      subActive={subActive}
    />
  ),
  [PREVENTATIVE_SCREENING.dental_checkup.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSDentalCheckupCleaning}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      emptyStateImageSrc="/empty-card-dental-checkup-cleaning.png"
      infoDescription="Dental cleaning, performed by dentists or dental hygienists, helps to maintain or achieve optimum oral health. The WHO cites oral health as integral to general health. Frequent trips to the dentist can also boost the prevention of diseases."
      action={action}
      subActive={subActive}
    />
  ),
  [PREVENTATIVE_SCREENING.skin_self_examination.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSSkinSelfExamination}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      emptyStateImageSrc="/empty-card-skin-self-examination.png"
      hasExternalReports={false}
      infoDescription="Skin screenings and self-examinations are important for detecting signs of skin cancer or other skin conditions. Examine the face, chest and belly, underarms, arms, hands, and fingernails. Check the scalp with a comb while facing away from the mirror. Use a portable mirror and a full-length mirror. Engage the help of a partner or family member, asking them to inspect the back and difficult-to-see areas. Examine the genital area, thighs, shins and feet, particularly between toes and under toe nails. Check the backs of thighs, calves and the bottoms of feet using the handheld mirror. The Mayo Clinic emphasises how detecting potential risk factors early and regularly attending screenings can help reduce the risk of developing serious health issues related to skin. Self-examinations, as guided by a doctor, should be done monthly to note any suspicious changes."
      action={action}
      subActive={subActive}
    />
  ),
  [PREVENTATIVE_SCREENING.breast_selF_evaluation.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSBreastSelfEvaluation}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      emptyStateImageSrc="/empty-card-breast-self-evaluation.png"
      hasExternalReports={false}
      infoDescription="A breast self-exam is an inspection of breasts conducted by an individual. The Mayo Clinic explains how the test increases breast disease/disorder awareness, where an individual uses their eyes and hands to detect changes in the appearance and feel of their breasts. It's advisable to consult a doctor for a demonstration on how to do one correctly. While breast self-exams are not sufficient in detecting breast abnormalities, a significant number of patients have reported that the first sign of a disease was a lump they discovered on their own. If you notice any changes in your breasts, discuss them with your doctor."
      action={action}
      subActive={subActive}
    />
  ),
  [PREVENTATIVE_SCREENING.testicular_self_examination.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSTesticularSelfExamination}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      emptyStateImageSrc="/empty-card-tse.png"
      hasExternalReports={false}
      infoDescription="A testicular self-exam is an inspection of the appearance and feel of your testicles. The Mayo Clinic explains that routine testicular self-exams can help you better understand the state of your testicles and identify changes or abnormalities. Please consult a doctor if you notice any changes."
      action={action}
      subActive={subActive}
    />
  ),
  [PREVENTATIVE_SCREENING.complete_blood_count.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSCompleteBloodCount}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      emptyStateImageSrc="/empty-card-cbc.png"
      infoDescription="A complete blood count (CBC) is a blood test used to find a wide range of conditions. A CBC measures red blood cells, which carry oxygen; white blood cells, which fight infection; hemoglobin, the oxygen-carrying protein in red blood cells; hematocrit, the amount of red blood cells in the blood; and platelets, which help blood to clot. As the Mayo Clinic points out, a CBC can show unusual increases or decreases in cell counts, that may point to a medical condition that calls for further examination."
      action={action}
      subActive={subActive}
    />
  ),
  [PREVENTATIVE_SCREENING.thyroid_test.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSThyroidTest}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      emptyStateImageSrc="/empty-card-thyroid-test.png"
      infoDescription="Several thyroid diseases produce symptoms, and a physical exam and blood test can aid in diagnosis. The Mayo Clinic explains this screening is helpful in understanding thyroid dysfunction and detecting hypo or hyperthyroidism. It's advisable to consult your doctor with tests, findings and further examinations."
      action={action}
      subActive={subActive}
    />
  ),
  [PREVENTATIVE_SCREENING.blood_sugar_test.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSBloodSugarTest}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      emptyStateImageSrc="/empty-card-blood-sugar-test.png"
      infoDescription="Please refer to a doctor for examination details and findings."
      action={action}
      subActive={subActive}
    />
  ),
  [PREVENTATIVE_SCREENING.allergy_test.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSAllergyTest}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      emptyStateImageSrc="/empty-card-allergy-test.png"
      infoDescription="Indian Pediatrics describes allergies as something that causes symptoms involving the cutaneous, respiratory, and gastrointestinal systems. There are several diagnostic options for allergy detection. Skin prick tests benefit aeroallergens, while oral challenge tests are excellent for detecting potential food allergies. Please consult a doctor for further examination and diagnosis."
      action={action}
      subActive={subActive}
    />
  ),
  [PREVENTATIVE_SCREENING.electrocardiogram_ecg.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSElectrocardiogramECG}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      emptyStateImageSrc="/empty-card-electrocardiogram.png"
      infoDescription="Please refer to a doctor for examination details and findings."
      action={action}
      subActive={subActive}
    />
  ),
  [PREVENTATIVE_SCREENING.two_d_echocardiogram.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PS2DEchocardiogram}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      emptyStateImageSrc="/empty-card-echocardiogram.png"
      infoDescription="Please refer to a doctor for examination details and findings."
      action={action}
      subActive={subActive}
    />
  ),
  [PREVENTATIVE_SCREENING.treadmill_test_tmt.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSTreadmillTestTMT}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      emptyStateImageSrc="/empty-card-treadmill-test.png"
      infoDescription="Please refer to a doctor for examination details and findings."
      action={action}
      subActive={subActive}
    />
  ),
  [PREVENTATIVE_SCREENING.colonoscopy.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSColonoscopy}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      emptyStateImageSrc="/empty-card-colonoscopy.png"
      infoDescription="According to Mayo Clinic a colonoscopy looks for changes such as swollen, irritated tissues, polyps or other diseases in the large intestine (colon) and rectum. If necessary, polyps or other types of abnormal tissue can be removed through the scope during a colonoscopy."
      action={action}
      subActive={subActive}
    />
  ),
  [PREVENTATIVE_SCREENING.sexually_transmitted_infections_sti.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSSexuallyTransmittedInfectionsSTI}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      emptyStateImageSrc="/empty-card-sti.png"
      infoDescription="Sexually transmitted diseases (STDs) are caused by sexually transmitted infections (STIs) and are spread mainly by sexual contact. Bacteria, viruses, and parasites all cause STIs. The Mayo Clinic defines how a sexually transmitted infection can spread from person to person by blood, sperm, vaginal or other bodily fluids. Common STIs include chlamydia, gonorrhoea, syphilis, herpes, human papillomavirus (HPV) and HIV, affecting all genders. Regular screenings for STIs, like blood tests, swabs, and Pap smears, are crucial to detect infections early and prevent their spread."
      action={action}
      subActive={subActive}
    />
  ),
  [PREVENTATIVE_SCREENING.lipid_profile.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSLipidProfile}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      action={action}
      subActive={subActive}
      emptyStateImageSrc="/empty-card-lipid-profile.png"
      infoDescription="The Mayo Clinic defines a lipid profile as a blood test that measures the amount of cholesterol and triglycerides in blood, including total cholesterol, HDL, LDL and triglycerides (fat). Elevated LDLs and triglycerides may cause plaque build-up in arteries, whereas HDLs absorb cholesterol and transport it from the bloodstream to the liver, where it is flushed from the body. A cholesterol test helps doctors assess risk for coronary artery disease and other vascular diseases including stroke. It also helps doctors determine if lifestyle changes or medications are necessary."
    />
  ),
  [PREVENTATIVE_SCREENING.prostate_specific_antigen_psa.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSProstateSpecificAntigenPSA}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      action={action}
      subActive={subActive}
      emptyStateImageSrc="/empty-card-mammogram.png"
      infoDescription="The Mayo Clinic describes the PSA test as a blood test used primarily to screen for prostate diseases. To decide whether to take a PSA test, please talk to your doctor about your risk factors and personal preferences."
    />
  ),
  [PREVENTATIVE_SCREENING.pap_smear.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSPapSmear}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      action={action}
      subActive={subActive}
      emptyStateImageSrc="/empty-card-pap-smear.png"
      infoDescription="A Pap smear is a procedure to test for cervical cancer in women. The Mayo Clinic explains how the procedure involves collecting cells from the outer part of the cervix and inspecting them for any abnormalities. Detecting cervical cancer early with a Pap smear increases the chances of a cure. A Pap smear can also reveal abnormalities in cervical cells that indicate cancer could occur in the future. The frequency of testing should occur based on a doctor's recommendations and will usually be based on family history or personal past medical history."
    />
  ),
  [PREVENTATIVE_SCREENING.mammogram.value]: (
    masterScreeningType: MasterScreening,
    action: string,
    subActive: string
  ) => (
    <PreventativeScreeningEntryList
      masterLinkId={PREVENTATIVE_SCREENING_LINK_ID.PSMammogram}
      masterScreeningType={masterScreeningType}
      screeningName={masterScreeningType?.value ?? ''}
      action={action}
      subActive={subActive}
      emptyStateImageSrc="/empty-card-mammogram.png"
      infoDescription="The Mayo Clinic defines a mammogram as an X-ray image of breasts that can be used either for screening or diagnostic purposes, to investigate symptoms or unusual findings on other imaging tests. During a mammogram, breasts are compressed between two firm surfaces to spread out the breast tissue. Mammograms play a key role in vital disease screenings, detecting disorders before they cause signs and symptoms. Mammograms have been shown to reduce the risk of dying of certain diseases."
    />
  ),
};

export function PreventativeScreeningTab({
  masterScreeningType,
  onClose,
  onBack,
  action,
  subActive,
}: {
  masterScreeningType: MasterScreening;
  onClose: () => void;
  onBack: () => void;
  action: string;
  subActive: string;
}) {
  return (
    <Stack
      py="4"
      height="full"
      width="full"
    >
      <Flex justifyContent="space-between">
        <HStack>
          <ChevronLeft
            onClick={onBack}
            cursor="pointer"
            size={24}
          />
          <SidebarHeading>
            {masterScreeningType?.value === 'Sexually Transmitted Infections (STI)'
              ? 'Sexually Transmitted Infections'
              : masterScreeningType?.value}
          </SidebarHeading>
        </HStack>
        <SidebarCloseButton onClick={onClose} />
      </Flex>
      <Suspense fallback={<FluentHealthLoader />}>
        {masterScreeningType?.value &&
          ACTION_BUTTON_RESOLVER_LIST[masterScreeningType.value](masterScreeningType, action, subActive)}
      </Suspense>
    </Stack>
  );
}

export function SidebarMenuItem({
  title,
  rightLabel,
  onClick,
}: {
  title: string;
  rightLabel?: any;
  onClick: () => void;
}) {
  const theme = useTheme();
  return (
    <Flex
      align="center"
      cursor="pointer"
      borderRadius="lg"
      px="2"
      py="3"
      _hover={{
        bgColor: 'fluentHealthSecondary.500',
      }}
      onClick={onClick}
      justify="space-between"
    >
      <Text
        lineHeight="1"
        fontSize="lg"
        fontWeight="medium"
      >
        {title}
      </Text>
      <Flex
        gap="4px"
        alignItems="center"
        flexShrink="0"
      >
        {rightLabel && (
          <Text
            color="gray.300"
            lineHeight="1"
            flexShrink="0"
          >
            {rightLabel}
          </Text>
        )}
        <ChevronRight
          size={20}
          color={theme.colors.papaya[600]}
        />
      </Flex>
    </Flex>
  );
}

export default function ProfileSidebarPreventativeScreenings({ onClose, subActive, action }: ISidebarProps) {
  const [currentScreeningTypeMenu, setCurrentScreeningTypeMenu] = useState<any>(null);
  const navigate = useNavigate();
  const { PROFILE, EHR, PREVENTATIVE_SCREENING: PREVENTATIVE_SCREENING_ROUTE } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;
  const { authenticatedUser } = useAuthService();
  const [psList, setPslist] = useState<any>({});
  const identifierValues = preventativeScreeningConstants.map((item: any) => {
    const key = Object.keys(item)[0];
    return item[key].identifier.value;
  });

  const { preventativeScreeningList } = useGetPrevenativeScreening(authenticatedUser?.id, identifierValues);
  function transformKeys(obj: Record<string, any>): Record<string, any> {
    const transformedObject: Record<string, any> = {};

    Object.keys(obj).forEach((key) => {
      const newKey = key
        .replace(/^ps/, 'ps:')
        .replace(/([A-Z])/g, '-$1')
        .toLowerCase();
      transformedObject[newKey] = obj[key];
    });

    return transformedObject;
  }
  useEffect(() => {
    setCurrentScreeningTypeMenu(null);
    const updatedObject = transformKeys(preventativeScreeningList);
    setPslist(updatedObject);
  }, []);

  useEffect(() => {
    if (subActive) {
      const category = Object.values(PREVENTATIVE_SCREENING).find((value) => value.route === subActive);
      if (category) {
        setCurrentScreeningTypeMenu({ value: category.value, name: category.name });
      }
    }
  }, [subActive]);

  const getRightLabel = (name: string) => {
    const type = PREVENTATIVE_SCREENING_LINK_ID?.[name]?.type;

    const procedures = psList?.[type] || [];

    const procedure = procedures?.[0];

    const performedDateTime = procedure?.performedDateTime;

    return performedDateTime && dayjs(performedDateTime).isValid()
      ? dayjs(performedDateTime).format('DD.MM.YYYY')
      : undefined;
  };

  return (
    <Container
      position="relative"
      height="full"
      overflowY="scroll"
      overflowX="hidden"
      className="hide-scrollbar"
    >
      {currentScreeningTypeMenu ? (
        <Suspense
          fallback={
            <FluentHealthLoader
              position="absolute"
              top={0}
              bottom={0}
              left={0}
              right={0}
              my="auto"
            />
          }
        >
          <PreventativeScreeningTab
            masterScreeningType={currentScreeningTypeMenu}
            onClose={onClose}
            onBack={() => {
              setCurrentScreeningTypeMenu(null);
              navigate(`/${PROFILE}/${EHR}/${PREVENTATIVE_SCREENING_ROUTE}/${VIEW}`, { replace: true });
            }}
            action={action ?? 'view'}
            subActive={subActive ?? ''}
          />
        </Suspense>
      ) : (
        <Stack
          py="4"
          height="full"
        >
          <Flex justifyContent="space-between">
            <SidebarHeading>Key Health Tests</SidebarHeading>
            <SidebarCloseButton onClick={onClose} />
          </Flex>
          <Text
            fontSize="14"
            fontWeight="normal"
            letterSpacing="-0.28px"
            lineHeight="20px"
            color="charcoal.60"
          >
            Some of the fields shown may not be applicable to you. Respond to the ones that apply to your sex assigned
            at birth.
          </Text>
          <Flex
            alignItems="center"
            direction="column"
            overflowY="scroll"
            className="hide-scrollbar"
          >
            <Card
              bgColor="periwinkle.100"
              borderRadius="xl"
              border="1px solid"
              borderColor="fluentHealthSecondary.300"
              boxShadow="0px 1px 4px 0px rgba(73, 90, 228, 0.12);"
              width="full"
              mt="6"
            >
              <VStack
                alignItems="left"
                p="2"
              >
                {Object.values(PREVENTATIVE_SCREENING).map(({ name, value, route }) => (
                  <React.Fragment key={name}>
                    <SidebarMenuItem
                      title={value}
                      rightLabel={getRightLabel(name)}
                      onClick={() => {
                        setCurrentScreeningTypeMenu({ name, value });
                        navigate(`/${PROFILE}/${EHR}/${PREVENTATIVE_SCREENING_ROUTE}/${route}/${VIEW}`);
                      }}
                    />
                  </React.Fragment>
                ))}
              </VStack>
            </Card>
          </Flex>
        </Stack>
      )}
    </Container>
  );
}
