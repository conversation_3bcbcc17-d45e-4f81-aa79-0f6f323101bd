import {
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  HStack,
  IconButton,
  Input,
  Radio,
  RadioGroup,
  Stack,
  Switch,
  Text,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod/dist/zod';
import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm, useFormContext } from 'react-hook-form';
import { z } from 'zod';
import { DOCUMENT_REF, NavigationHelper, PATIENT_DEFINED } from '@user/lib/constants';
import { useMedication } from '@user/lib/medplum-state';
import { MedicationsEventProps, recordMedicationsEvents } from '@user/lib/events-analytics-manager';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';

import { useAnalyticsService, useAuthService } from '@lib/state';
import { <PERSON><PERSON><PERSON><PERSON>ield, SuggestionOptionProps } from '../../../../../components/ui/Form';
import { LinkedDocumentsCard } from '../LinkedDocumentsCard';
import { MedicalRecordSelect } from '../MedicalRecordSelect';
import { SearchableSelect, Select, SelectOptionProps } from '../../../../../components/ui/Select';
import {
  FH_CODE_SYSTEM_FACT,
  FH_CODE_SYSTEM_FLUENT_HEALTH_UI,
  FH_STRUCTURE_DEFINITION_CLINICALSTATUS,
  SNOMED_URL,
} from 'src/constants/medplumConstants';
import { useExtractDocumentResource } from 'src/app/medical-records/lib/state';
import { isDuplicatePresentMedication } from '@lib/utils/utils';

interface TimeDoseInputProps {
  label: string;
  value: number;
  onIncrement: () => void;
  onDecrement: () => void;
  onChange: (value: string) => void;
}
const timingMapping: Record<string, string> = {
  morning: 'MORN',
  afternoon: 'AFT',
  evening: 'EVE',
  night: 'NIGHT',
};

const formControlStyles = {
  border: '1px solid',
  borderRadius: '12px',
  px: 4,
  py: 2,
  borderColor: 'iris.500',
  mb: 2,
};
const inputStyles = {
  w: '50px',
  h: '25px',
  min: 0,
  max: 999,
  borderColor: 'iris.500',
  variant: 'flushed',
};
const VALUE_SET_DOSAGE_ADDITIONAL_INSTRUCTION = 'http://hl7.org/fhir/r4/valueset-additional-instruction-codes';
const BEFORE_FOOD_CODE = '311501008';
const BEFORE_FOOD_DISPLAY = 'Before Food';
const AFTER_FOOD_CODE = '311504000';
const AFTER_FOOD_DISPLAY = 'After Food';

const customOption = { label: 'My medication is not listed', value: 'MY_medication_NOT_LISTED' };
export function NoOptionsMessageComponent({ onClick }: { onClick: () => void }) {
  return (
    <Text
      sx={{
        width: '100%',
        textAlign: 'left',
        color: 'fluentHealthText.100',
        cursor: 'pointer',
      }}
      onClick={onClick}
    >
      My medication is not listed
    </Text>
  );
}

function MinusIcon({ disabled }: { disabled: boolean }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
        stroke={disabled ? '#A9AEAF' : '#14181A'}
        fill={disabled ? 'none' : '#fff'}
        strokeLinecap="round"
      />
      <path
        d="M16.2426 12.0005H7.75736"
        stroke={disabled ? '#A9AEAF' : '#14181A'}
        strokeLinecap="round"
      />
    </svg>
  );
}

function PlusIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
        fill="#fff"
        stroke="#14181A"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 8V16"
        stroke="#14181A"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 12H16"
        stroke="#14181A"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

function TimeDoseInput({ label, value, onIncrement, onDecrement, onChange }: TimeDoseInputProps): JSX.Element {
  return (
    <FormControl {...formControlStyles}>
      <HStack justifyContent="space-between">
        <FormLabel
          mb="0"
          color="fluentHealthText.200"
          fontWeight="400"
          textTransform="capitalize"
        >
          {label}
        </FormLabel>
        <HStack>
          <IconButton
            aria-label="Decrease"
            icon={<MinusIcon disabled={value === 0} />}
            size="xs"
            variant="ghost"
            isDisabled={value === 0}
            onClick={onDecrement}
          />
          <Input
            type="number"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            {...inputStyles}
            textAlign="center"
          />
          <IconButton
            aria-label="Increase"
            icon={<PlusIcon />}
            size="xs"
            variant="ghost"
            isDisabled={value === 999}
            onClick={onIncrement}
          />
        </HStack>
      </HStack>
    </FormControl>
  );
}

function getInitialFormData(medication: any) {
  const { item: items } = medication || {};
  const primary = medication?.medicationCodeableConcept?.coding?.[0] ?? { code: '', display: '' };
  const isCustom = primary?.code === `me:${PATIENT_DEFINED}`;
  return {
    id: medication?.id || '',
    drug_id: isCustom ? customOption.value : medication?.medicationCodeableConcept.coding[0].code,
    drug_name: isCustom ? customOption.label : medication?.medicationCodeableConcept.coding[0].display,
    dosageInstruction: items?.find((i: any) => i.linkId === 'medication-dosage-amount')?.answer?.[0]?.valueString,
    external_reports: medication?.derivedFrom ? useExtractDocumentResource(medication?.derivedFrom) : [],
    medication_administered: {
      label: medication?.dosage?.[0]?.route?.coding?.[0]?.display,
      value: medication?.dosage?.[0]?.route?.coding?.[0]?.code,
    },
    medication_date: medication?.effectivePeriod?.start,
    medication_end: medication?.effectivePeriod?.end,
    medication_status: medication?.extension?.[0]?.valueCodeableConcept?.coding?.[0]?.code === 'active',
    custom_medication: isCustom ? primary.display : '',
  };
}

function MedicationSelect({
  medicationOptions,
  trackEventInFlow,
  onSearch,
  medication,
  setShowCustomInput,
  setHasChanges,
}: {
  medicationOptions: any;
  trackEventInFlow: any;
  onSearch: (searchVal: string) => void;
  medication: any;
  setShowCustomInput: (flag: boolean) => void;
  setHasChanges: (flag: boolean) => void;
}) {
  const form: any = useFormContext();
  const [conditionValue, setConditionValue] = useState<SuggestionOptionProps | null>({
    label: form.watch('drug_name'),
    value: form.watch('drug_id'),
  });
  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [showNoOptions, setShowNoOptions] = useState(false);
  const handleNoOptionsClick = useCallback(() => {
    setConditionValue(customOption);
    setShowCustomInput(true);
    setInputValue('');
    setMenuIsOpen(false);
    form.setValue('drug_name', customOption.label);
    form.setValue('drug_id', customOption.value);
    form.trigger(['drug_name', 'drug_id']);
  }, [setShowCustomInput]);

  const onChange = useCallback(
    (option: SelectOptionProps | any) => {
      if (option) {
        form.setValue('drug_id', option.value);
        form.setValue('drug_name', option.label);
        form.trigger(['drug_id', 'drug_name']);
        setConditionValue(option);
        setHasChanges(true);
        if (!medication) {
          recordMedicationsEvents(trackEventInFlow, {
            EventName: 'MedicationsAddInProgName',
            me_name: option?.label,
          });
        }
      } else {
        form.setValue('drug_id', '');
        form.setValue('drug_name', '');
        form.trigger(['drug_id', 'drug_name']);
        setConditionValue(null);
        setHasChanges(true);
      }
      setShowCustomInput(false);
    },
    [medicationOptions]
  );

  const handleFocus = useCallback(() => {
    if (conditionValue?.value === customOption.value) {
      setConditionValue(null);
      form.setValue('medication', '');
      setShowCustomInput(false);
    }
  }, [conditionValue, form, setShowCustomInput]);

  const noOptionsMessageFn = useCallback(() => {
    if (!showNoOptions) {
      setTimeout(() => {
        setShowNoOptions(true);
      }, 3000);
      return null;
    }
    return <NoOptionsMessageComponent onClick={handleNoOptionsClick} />;
  }, [handleNoOptionsClick, showNoOptions]);

  useEffect(() => {
    setShowNoOptions(false);
  }, [inputValue]);

  return (
    <FormControl
      variant="floating"
      isInvalid={!!form.formState.errors?.drug_id}
    >
      <SearchableSelect
        labelText="Name of medication*"
        value={conditionValue}
        options={medicationOptions}
        onChange={onChange}
        onSearch={onSearch}
        isMedicationSelect
        isClearable={false}
        onFocus={handleFocus}
        menuIsOpen={menuIsOpen}
        onMenuOpen={() => setMenuIsOpen(true)}
        onMenuClose={() => setMenuIsOpen(false)}
        inputValue={inputValue}
        onInputChange={(newValue: any) => {
          setInputValue(newValue);
          onSearch(newValue);
        }}
        noOptionsMessage={noOptionsMessageFn}
      />
      <FormErrorMessage> {form?.formState?.errors?.drug_id?.message} </FormErrorMessage>
    </FormControl>
  );
}

export default function MedicationForm({
  medication,
  closeDialog,
  medicationOptions,
  medicationAdministeredOptions,
  onSearch,
  formSubmiting,
}: {
  medication: any;
  // answerList: any;
  closeDialog: () => void;
  medicationOptions: any;
  medicationAdministeredOptions: any;
  onSearch: (searchText: string) => void;
  formSubmiting: (incoming: boolean) => void;
}) {
  const toast = useToast();
  const navigate = useNavigate();
  const startDatePickerPopover = useDisclosure();
  const endDatePickerPopover = useDisclosure();
  const [hasChanges, setHasChanges] = useState(false);
  const [isLoading] = useState<boolean>(false);
  const { authenticatedUser } = useAuthService();
  const { medicationList, updateMedication, addMedication } = useMedication(authenticatedUser?.id);
  const { trackEventInFlow } = useAnalyticsService();
  const [showCustomInput, setShowCustomInput] = useState(
    medication?.medicationCodeableConcept?.coding[0].code === `me:${PATIENT_DEFINED}` || false
  );
  const form = useForm({
    mode: 'onChange',
    defaultValues: getInitialFormData(medication),
    resolver: zodResolver(
      z.object({
        drug_id: z
          .string()
          .min(1, 'Medication type is required')
          .refine((value) => {
            if (value === customOption.value) {
              form.clearErrors('drug_id.value');
              return true;
            }
            const duplicateId = isDuplicatePresentMedication(value, medicationList, medication);
            return !duplicateId;
          }, 'This Medication already exists'),
        drug_name: z.string().trim().min(1),
        medication_administered: z
          .object({ value: z.string().trim().optional(), label: z.string().trim().optional() })
          .optional(),
        medication_administered_label: z.string().trim().optional(),
        medication_date: z.string().trim().nullable().optional(),
        medication_end: z.string().trim().nullable().optional(),
        medication_status: z.boolean().optional(),
        custom_medication: showCustomInput ? z.string().min(1, 'This field is required') : z.string().optional(),
      })
    ),
  });
  const defaultSchedule = (medication?.dosage || []).reduce(
    (acc: any, dose: any) => {
      const whenList = dose?.timing?.repeat?.when || []; // Ensure it's an array
      const doseValue = dose?.doseAndRate?.[0]?.doseQuantity?.value || 0;
      const duration = dose?.timing?.repeat?.boundsDuration?.value ?? acc.duration;
      const foodDisplay = dose?.additionalInstruction?.[0]?.coding?.[0]?.display;
      const foodTiming =
        acc.foodTiming ||
        (foodDisplay === AFTER_FOOD_DISPLAY ? 'after' : foodDisplay === BEFORE_FOOD_DISPLAY ? 'before' : '');
      whenList.forEach((when: string) => {
        switch (when) {
          case timingMapping.morning:
            acc.morning += doseValue;
            break;
          case timingMapping.afternoon:
            acc.afternoon += doseValue;
            break;
          case timingMapping.evening:
            acc.evening += doseValue;
            break;
          case timingMapping.night:
            acc.night += doseValue;
            break;
          default:
            break;
        }
      });
      acc.duration = duration;
      acc.foodTiming = foodTiming;
      return acc;
    },
    { morning: 0, afternoon: 0, evening: 0, night: 0, duration: 0, foodTiming: '' }
  );
  const [schedule, setSchedule] = useState(defaultSchedule);
  const scheduleMapping: Record<
    keyof typeof schedule,
    { eventName: MedicationsEventProps['EventName']; propName: keyof MedicationsEventProps }
  > = {
    morning: { eventName: 'MedicationsAddInProgScheduleMorning', propName: 'me_dosage_morning' },
    afternoon: { eventName: 'MedicationsAddInProgScheduleAfternoon', propName: 'me_dosage_afternoon' },
    evening: { eventName: 'MedicationsAddInProgScheduleEvening', propName: 'me_dosage_evening' },
    night: { eventName: 'MedicationsAddInProgScheduleNight', propName: 'me_dosage_night' },
    duration: { eventName: 'MedicationsAddInProgScheduleDuration', propName: 'me_dosage_duration' },
  };
  const updateMedicationEventForKey = (key: keyof typeof schedule, newValue: number) => {
    const mappingItem = scheduleMapping[key];
    if (mappingItem && !medication) {
      recordMedicationsEvents(trackEventInFlow, {
        EventName: mappingItem.eventName,
        [mappingItem.propName]: newValue,
      });
    }
  };
  const handleIncrement = (key: keyof typeof schedule) => {
    setSchedule((prev: any) => {
      const current = Number(prev[key]);
      const newVal = Math.min(999, current + 1);
      updateMedicationEventForKey(key, newVal);
      return { ...prev, [key]: newVal };
    });
    form.setValue('medication_date', form.watch('medication_date'));
    form.trigger('medication_date');
  };
  const handleDecrement = (key: keyof typeof schedule) => {
    setSchedule((prev: any) => {
      const current = Number(prev[key]);
      const newVal = Math.max(0, current - 1);
      updateMedicationEventForKey(key, newVal);
      return { ...prev, [key]: newVal };
    });
    form.setValue('medication_date', form.watch('medication_date'));
    form.trigger('medication_date');
  };

  const handleDoseChange = (key: keyof typeof schedule, value: string) => {
    const numericValue = Number(value);
    if (!Number.isNaN(numericValue) && numericValue >= 0 && numericValue <= 999) {
      setSchedule((prev: any) => {
        updateMedicationEventForKey(key, numericValue);
        return { ...prev, [key]: numericValue };
      });
    }
    form.setValue('medication_date', form.watch('medication_date'));
    form.trigger('medication_date');
  };
  const startDateField = form.watch('medication_date');
  const endDateField = form.watch('medication_end');
  const datePickerChangeHandler = (date: Date | null, type: string) => {
    if (type === 'start') {
      if (dayjs(date).isValid()) {
        form.setValue('medication_date', dayjs(date).format('YYYY-MM-DD'));
        setHasChanges(true);
        if (!medication) {
          recordMedicationsEvents(trackEventInFlow, {
            EventName: 'MedicationsAddInProgStartDate',
            me_start_date: dayjs(date).format('YYYY/MM/DD'),
          });
        }
      } else {
        form.setValue('medication_date', '');
        setHasChanges(true);
      }
      form.trigger('medication_date');
      startDatePickerPopover.onClose();
    } else {
      if (dayjs(date).isValid()) {
        form.setValue('medication_end', dayjs(date).format('YYYY-MM-DD'));
        setHasChanges(true);
        if (!medication) {
          recordMedicationsEvents(trackEventInFlow, {
            EventName: 'MedicationsAddInProgEndDate',
            me_end_date: dayjs(date).format('YYYY/MM/DD'),
          });
        }
      } else {
        form.setValue('medication_end', '');
        setHasChanges(true);
      }
      endDatePickerPopover.onClose();
    }
  };
  const datePickerClearHandler = (type: string) => {
    if (type === 'start') {
      form.setValue('medication_date', '');
      startDatePickerPopover.onClose();
    } else {
      form.setValue('medication_end', '');
      endDatePickerPopover.onClose();
    }
  };
  const {
    handleSubmit,
    register,
    formState: { isSubmitting, isValid },
    watch,
  }: any = form;
  const takingAdministered = watch('medication_administered');
  const externalReportsField = watch('external_reports');
  const onMedicationAdministeredSelect = useCallback((value: SelectOptionProps | any) => {
    form.setValue('medication_administered', value);
    form.trigger('medication_administered');
    setHasChanges(true);
    if (!medication) {
      recordMedicationsEvents(trackEventInFlow, {
        EventName: 'MedicationsAddInProgAdministered',
        me_administered: value?.label,
      });
    }
  }, []);
  async function onSubmit(addedMedication: any) {
    try {
      formSubmiting(true);
      const groupedDosage: Record<number, string[]> = {};
      Object.entries(schedule).forEach(([key, value]) => {
        if (!['morning', 'afternoon', 'evening', 'night'].includes(key) || Number(value) <= 0) return;
        if (!groupedDosage[Number(value)]) {
          groupedDosage[Number(value)] = [];
        }
        groupedDosage[Number(value)].push(key);
      });

      const dosageInstructions = Object.entries(groupedDosage).map(([doseValue, times]) => {
        const whenArray = times.map((key) => timingMapping[key]);

        const dosage: any = {
          timing: {
            repeat: {
              boundsDuration: {
                value: schedule.duration,
                unit: 'days',
              },
              when: whenArray,
            },
          },
          doseAndRate: [
            {
              doseQuantity: { value: Number(doseValue) },
            },
          ],
          additionalInstruction: [],
        };
        if (times.some((key) => key !== 'night')) {
          dosage.route = {
            coding: [
              {
                system: SNOMED_URL,
                code: addedMedication?.medication_administered?.value,
                display: addedMedication?.medication_administered?.label,
              },
            ],
          };
        }

        return dosage;
      });
      if (schedule.foodTiming) {
        const additionalCoding = {
          system: VALUE_SET_DOSAGE_ADDITIONAL_INSTRUCTION,
          code: schedule.foodTiming === 'before' ? BEFORE_FOOD_CODE : AFTER_FOOD_CODE,
          display: schedule.foodTiming === 'before' ? BEFORE_FOOD_DISPLAY : AFTER_FOOD_DISPLAY,
        };

        dosageInstructions.forEach((dosage: any) => {
          dosage.additionalInstruction.push({
            coding: [additionalCoding],
          });
        });
      }
      let coding: any = [];
      if (addedMedication?.drug_id === customOption.value) {
        coding = [
          {
            system: SNOMED_URL,
            code: `me:${PATIENT_DEFINED}`,
            display: addedMedication.custom_medication,
          },
        ];
      } else {
        coding = [
          {
            system: SNOMED_URL,
            code: addedMedication.drug_id,
            display: addedMedication.drug_name,
          },
        ];
      }
      const payload: {
        resourceType: string;
        id?: string;
        identifier: { system: string; value: string }[];
        status: string;
        medicationCodeableConcept: { coding: any };
        subject: { reference: string };
        effectivePeriod: { start: any; end?: any };
        dosage: any[];
        derivedFrom?: { reference: string }[];
        extension?: { url: string; valueCodeableConcept: { coding: any } }[];
      } = {
        resourceType: 'MedicationStatement',
        ...(medication?.id ? { id: medication.id } : {}),
        identifier: [
          {
            system: FH_CODE_SYSTEM_FACT,
            value: 'Medications',
          },
        ],
        status: addedMedication?.medication_status ? 'active' : addedMedication?.medication_end ? 'stopped' : 'active',
        medicationCodeableConcept: {
          coding,
        },
        subject: {
          reference: `Patient/${authenticatedUser?.id}`,
        },
        effectivePeriod: {
          start: addedMedication.medication_date,
          ...(!addedMedication?.medication_status && addedMedication?.medication_end
            ? { end: addedMedication.medication_end }
            : {}),
        },
        dosage: dosageInstructions,
      };
      if (externalReportsField?.length) {
        payload.derivedFrom = externalReportsField.map((item: any) => ({
          reference: `${DOCUMENT_REF}/${item.id}`,
        }));
      }
      const status = addedMedication.medication_status
        ? { code: 'active', display: 'Active' }
        : { code: 'inactive', display: 'Inactive' };

      payload.extension = [
        {
          url: FH_STRUCTURE_DEFINITION_CLINICALSTATUS,
          valueCodeableConcept: {
            coding: [{ system: FH_CODE_SYSTEM_FLUENT_HEALTH_UI, ...status }],
          },
        },
      ];
      if (medication) {
        await updateMedication({
          payloadMedication: payload,
        });
      } else {
        await addMedication({ payload });
      }

      recordMedicationsEvents(trackEventInFlow, {
        EventName: medication ? 'MedicationsEdited' : 'MedicationsAddCompleted',
        me_entry_point: 'my_health_profile',
        me_name: addedMedication?.drug_name,
        me_status: addedMedication?.medication_status,
        me_start_date: dayjs(addedMedication?.medication_date).format('YYYY/MM/DD'),
        me_end_date: addedMedication?.medication_end && dayjs(addedMedication?.medication_end).format('YYYY/MM/DD'),
        me_dosage_morning: Number(schedule.morning),
        me_dosage_afternoon: Number(schedule.afternoon),
        me_dosage_evening: Number(schedule.evening),
        me_dosage_night: Number(schedule.night),
        me_dosage_duration: Number(schedule.duration),
        me_food: schedule.foodTiming,
        me_administered: addedMedication?.medication_administered?.label,
        me_record_added: addedMedication?.external_reports?.length,
      });

      toast({
        title: `${medication ? 'Successfully updated' : 'Successfully added'} medication`,
        status: 'success',
        duration: 4000,
        isClosable: true,
      });

      closeDialog?.();
    } catch (_) {
      toast({
        title: 'Something went wrong. Please try again.',
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    } finally {
      formSubmiting(false);
      navigate(NavigationHelper.getEhrView(false, 'medications-supplements', 'medication'));
    }
  }

  const removeAttachedMedicalRecordHandler = (record: any) => {
    form.setValue(
      'external_reports',
      externalReportsField.filter((item: any) => item.id !== record.id)
    );
  };
  useEffect(() => {
    const subscription = form.watch(() => {
      setHasChanges(true);
    });

    return () => subscription.unsubscribe();
  }, [form]);

  return (
    <FormProvider {...form}>
      <Box
        color="black"
        display="flex"
        flexDirection="column"
      />
      <Flex
        direction="column"
        gap="4"
        mt="4"
      >
        <Box mt="3">
          <Flex
            direction="column"
            gap="8"
          >
            <MedicationSelect
              medicationOptions={medicationOptions}
              trackEventInFlow={trackEventInFlow}
              onSearch={onSearch}
              medication={medication}
              setShowCustomInput={setShowCustomInput}
              setHasChanges={setHasChanges}
            />
            {showCustomInput && (
              <FormControl
                isInvalid={!!form.formState.errors.custom_medication}
                variant="floating"
              >
                <Input
                  defaultValue={form.watch('custom_medication') || ''}
                  placeholder=" "
                  {...form.register('custom_medication')}
                  onChange={(e) => {
                    form.setValue('custom_medication', e?.target?.value);
                    form.trigger('custom_medication');
                    setHasChanges(true);
                  }}
                  onBlurCapture={(e) => {
                    if (e.target.value && !medication) {
                      recordMedicationsEvents(trackEventInFlow, {
                        EventName: 'MedicationsAddInProgName',
                        me_name: e.target.value.toString(),
                      });
                    }
                  }}
                />
                <FormLabel>Enter the medication*</FormLabel>
              </FormControl>
            )}
          </Flex>
        </Box>
        <Box mt="5">
          <DatePickerField
            name="start_date"
            labelText="Start Date for this medication"
            errorText="This field is required"
            rules={{ required: true }}
            datePickerChangeHandler={(date) => datePickerChangeHandler(date, 'start')}
            datePickerClearHandler={() => datePickerClearHandler('start')}
            datePickerPopover={startDatePickerPopover}
            isClearDateButtonDisabled={startDateField?.length === 0}
            selected={
              dayjs(form.watch('medication_date')).isValid() ? dayjs(form.watch('medication_date')).toDate() : null
            }
            popoverProps={{ placement: 'bottom-start' }}
            maxDate={endDateField && dayjs(endDateField).isValid() ? dayjs(endDateField).toDate() : new Date()}
            {...register('medication_date')}
          />
          <Flex
            alignItems="center"
            justifyContent="space-between"
            borderBottom="1px solid var(--chakra-colors-iris-500)"
            color="var(--chakra-colors-iris-500)"
            mt={8}
          >
            <FormLabel
              fontSize="18px"
              fontWeight={400}
            >
              Are you still taking this medication?
            </FormLabel>
            <Switch
              size="md"
              mb={2}
              isChecked={form.watch('medication_status')}
              onChange={(e) => {
                form.setValue('medication_status', e.target.checked);
                setHasChanges(true);
                if (!medication) {
                  recordMedicationsEvents(trackEventInFlow, {
                    EventName: 'MedicationsAddInProgStatus',
                    me_status: e.target.checked,
                  });
                }
              }}
            />
          </Flex>
          {!form.watch('medication_status') && (
            <Flex mt={10}>
              <DatePickerField
                name="end_date"
                labelText="End Date for this medication"
                datePickerChangeHandler={(date) => datePickerChangeHandler(date, 'end')}
                datePickerClearHandler={() => datePickerClearHandler('end')}
                datePickerPopover={endDatePickerPopover}
                isClearDateButtonDisabled={endDateField?.length === 0}
                selected={
                  dayjs(form.watch('medication_end')).isValid() ? dayjs(form.watch('medication_end')).toDate() : null
                }
                popoverProps={{ placement: 'bottom-start' }}
                minDate={
                  startDateField && dayjs(startDateField).isValid() ? dayjs(startDateField).toDate() : new Date()
                }
                maxDate={new Date()}
                {...register('medication_end')}
              />
            </Flex>
          )}
        </Box>
        <Box>
          <Text
            color="iris.500"
            mt={3}
            mb={1}
            fontSize="18px"
          >
            Schedule of medication
          </Text>
          {(['morning', 'afternoon', 'evening', 'night'] as const)?.map((time) => (
            <TimeDoseInput
              key={time}
              label={time}
              value={schedule[time]}
              onIncrement={() => handleIncrement(time)}
              onDecrement={() => handleDecrement(time)}
              onChange={(value) => handleDoseChange(time, value)}
            />
          ))}
          <FormControl {...formControlStyles}>
            <HStack
              justifyContent="space-between"
              pr="32px"
            >
              <FormLabel
                mb="0"
                color="fluentHealthText.200"
                fontWeight="400"
              >
                Duration (in days)
              </FormLabel>
              <Input
                type="number"
                value={String(schedule.duration)}
                onChange={(e) => handleDoseChange('duration', e.target.value)}
                {...inputStyles}
                textAlign="center"
              />
            </HStack>
          </FormControl>
          <FormControl {...formControlStyles}>
            <HStack justifyContent="space-between">
              <RadioGroup
                onChange={(value) => {
                  setSchedule((prev: any) => ({ ...prev, foodTiming: value }));
                  setHasChanges(true);
                  if (!medication) {
                    recordMedicationsEvents(trackEventInFlow, {
                      EventName: 'MedicationsAddInProgScheduleFood',
                      me_food: value,
                    });
                  }
                }}
                value={schedule.foodTiming}
              >
                <Stack
                  direction="row"
                  spacing={6}
                >
                  <Radio
                    color="fluentHealthText.200"
                    value="before"
                  >
                    Before food
                  </Radio>
                  <Radio
                    color="fluentHealthText.200"
                    value="after"
                  >
                    After food
                  </Radio>
                </Stack>
              </RadioGroup>
            </HStack>
          </FormControl>
        </Box>
        <Box mt="3">
          <Select
            labelText="How is this medication administered?"
            value={medicationAdministeredOptions?.find((item: any) => item.value === takingAdministered?.value)}
            onChange={onMedicationAdministeredSelect}
            options={medicationAdministeredOptions}
            menuPosition="fixed"
            isSearchable={false}
            isClearable
          />
        </Box>
        <Box mt="3">
          <MedicalRecordSelect
            onSelectExtra={(e) =>
              recordMedicationsEvents(trackEventInFlow, {
                EventName: 'MedicationsAddInProgRecordsAdded',
                me_record_added: !!e?.length,
              })
            }
          />
          {externalReportsField.length > 0 && (
            <Box mt="3">
              <LinkedDocumentsCard
                records={externalReportsField}
                onRemove={removeAttachedMedicalRecordHandler}
                showRemoveButton
              />
            </Box>
          )}
        </Box>
        <HStack justifyContent="flex-end">
          <Button
            isDisabled={!isValid || !hasChanges}
            isLoading={isSubmitting || isLoading}
            onClick={handleSubmit(onSubmit)}
          >
            {medication ? 'Save' : 'Add'}
          </Button>
        </HStack>
      </Flex>
    </FormProvider>
  );
}
