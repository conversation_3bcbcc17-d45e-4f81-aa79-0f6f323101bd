import { Card, Container, Flex, Stack, VStack, useTheme } from '@chakra-ui/react';
import React, { Suspense, useEffect, useState } from 'react';
import { ChevronLeft as ChevronLeftIcon } from 'react-feather';
import { useNavigate } from 'react-router-dom';

import { SidebarMenuItem } from '../SidebarMenuItem';
import { ISidebarProps } from '@lib/models/misc';
import { FluentHealthLoader } from '../../../../../components/FluentHealthLoader';
import { ProfileSidebarSupplements } from './ProfileSidebarSupplements';
import { SidebarCloseButton, SidebarHeading } from '../SidebarComponents';
import ProfileSidebarMedication from './ProfileSidebarMedication';
// import { useMedicationListLazyPagination, useSupplementList } from '../../../lib/state';
import { useAuthService } from '@lib/state';
import { useMasterQuestionnaireResponseList } from 'src/app/user/lib/medplum-state'; // useMasterQuestionnaireList,
import { MEDICATION_SUPPLEMENT, NavigationHelper, ROUTE_ACTIONS, ROUTE_VARIABLES } from 'src/app/user/lib/constants';
import { MEDPLUM_QUESTIONNAIRE } from '@lib/constants';
// import { getValueSetByMasterList, getValueSetForMedication, parseValueSetValue } from '@lib/utils/utils';

export default function SidebarMedicationAndSupplements({ onClose, isOpen, action, subActive }: ISidebarProps) {
  const theme = useTheme();
  const navigate = useNavigate();
  const { PROFILE, EHR, MEDICATIONS_SUPPLEMENTS, MEDICATION, SUPPLEMENT } = ROUTE_VARIABLES;
  const { ADD, VIEW, EDIT, DELETE } = ROUTE_ACTIONS;

  const [currentEntity, setCurrentEntity] = useState<any>();
  const { authenticatedUser } = useAuthService();
  // Medication
  // const { masterList } = useMasterQuestionnaireList(`${MEDPLUM_QUESTIONNAIRE}/${MEDICATION_SUPPLEMENT.MEDICATION}`);
  // const { masterList } = useMasterQuestionnaireList(`${MEDPLUM_QUESTIONNAIRE}/${MEDICATION_SUPPLEMENT.SUPPLEMENT}`);
  const { answerList } = useMasterQuestionnaireResponseList(
    `${MEDPLUM_QUESTIONNAIRE}/${MEDICATION_SUPPLEMENT.MEDICATION}`,
    authenticatedUser?.id,
    'myMedications'
  );
  const { answerList: answerListSupplements } = useMasterQuestionnaireResponseList(
    `${MEDPLUM_QUESTIONNAIRE}/${MEDICATION_SUPPLEMENT.SUPPLEMENT}`,
    authenticatedUser?.id,
    'mySupplement'
  );

  useEffect(() => {
    setCurrentEntity(null);
  }, [isOpen]);

  const handleGoBack = () => {
    setCurrentEntity(null);
    navigate(NavigationHelper.getEhrView(false, 'medications-supplements'));
  };

  useEffect(() => {
    let subActiveValue = subActive;
    if (action && ![ADD, VIEW, EDIT, DELETE].includes(action as ROUTE_ACTIONS) && !subActive) {
      subActiveValue = action;
    }
    if (!subActiveValue) return;
    setCurrentEntity(subActive === 'medication' ? 'Medications' : 'Supplements');

    if (action === ADD) {
      navigate(`/${PROFILE}/${EHR}/${MEDICATIONS_SUPPLEMENTS}/${subActiveValue}/${ADD}`);
    }
  }, [subActive]);

  const MENU_ITEMS_VALUES = {
    medication: {
      lastValue:
        answerList?.[0]?.item?.find((i: any) => i.linkId === 'name-of-medication')?.answer?.[0]?.valueCoding?.display ||
        '',
    },
    supplement: {
      lastValue:
        answerListSupplements?.[0]?.item?.find((i: any) => i.linkId === 'name-of-supplement')?.answer?.[0]?.valueCoding
          ?.display || '',
    },
  };

  return (
    <Container
      position="relative"
      height="full"
      overflowY="scroll"
      className="hide-scrollbar"
    >
      <Stack
        py="4"
        height="full"
      >
        <Flex justifyContent="space-between">
          <Flex
            gap="8px"
            alignItems="center"
            cursor="pointer"
            onClick={handleGoBack}
          >
            {currentEntity && (
              <ChevronLeftIcon
                size={24}
                color={theme.colors.fluentHealthText[100]}
              />
            )}
            <SidebarHeading mb={currentEntity ? '0px' : '20px'}>
              {currentEntity ?? 'Medications and Supplements'}
            </SidebarHeading>
          </Flex>
          <SidebarCloseButton onClick={onClose} />
        </Flex>
        {!currentEntity && (
          <Card
            bgColor="periwinkle.100"
            borderRadius="xl"
            borderWidth="1px"
            borderStyle="solid"
            borderColor="periwinkle.400"
            boxShadow="0px 1px 4px 0px rgba(73, 90, 228, 0.12)"
            gap="0"
            w="full"
          >
            <VStack
              alignItems="left"
              p="2"
              spacing={1}
            >
              <SidebarMenuItem
                title="Medications"
                onClick={() => {
                  setCurrentEntity('Medications');
                  navigate(`/${PROFILE}/${EHR}/${MEDICATIONS_SUPPLEMENTS}/${MEDICATION}/${VIEW}`);
                }}
                rightLabel={MENU_ITEMS_VALUES.medication.lastValue ?? undefined}
              />
              <SidebarMenuItem
                title="Supplements"
                onClick={() => {
                  setCurrentEntity('Supplements');
                  navigate(`/${PROFILE}/${EHR}/${MEDICATIONS_SUPPLEMENTS}/${SUPPLEMENT}/${VIEW}`);
                }}
                rightLabel={MENU_ITEMS_VALUES.supplement.lastValue ?? undefined}
              />
            </VStack>
          </Card>
        )}
        <Suspense fallback={<FluentHealthLoader />}>
          {currentEntity !== null &&
            (currentEntity === 'Medications' ? (
              <ProfileSidebarMedication action={action} />
            ) : (
              <ProfileSidebarSupplements action={action} />
            ))}
        </Suspense>
      </Stack>
    </Container>
  );
}
