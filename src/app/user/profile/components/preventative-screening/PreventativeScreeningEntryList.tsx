/* eslint-disable no-nested-ternary */
import { Flex, Spacer, Stack, useDisclosure, useToast } from '@chakra-ui/react';
import React, { Suspense, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { NavigationHelper, ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';
import { recordPreventativeScreeningEvents } from '@user/lib/events-analytics-manager';

import { FormSkeleton } from '../../../../../components/ui/Form';
import { MODAL_VARIANTS, Modal } from '../../../../../components/Modal';
import { SidebarEmptyState } from '../SidebarEmptyState';
import { MasterScreening } from '@lib/models/screening';
import { Form } from './Form';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { ScreeningCard } from './ScreeningCard';
import { SidebarAddButton, SidebarHelperTooltip } from '../SidebarComponents';
import { addArticle, findIdentifiersByType } from '@lib/utils/utils';
import { useGetPrevenativeScreening, usePreventativeScreening } from 'src/app/user/lib/medplum-state';
import { Procedure } from 'src/gql/graphql';
import { LinkedDocumentsCard, LinkedDocumentsLabel } from '../LinkedDocumentsCard';
import { useExtractDocumentResource } from 'src/app/medical-records/lib/state';

const screeningMapping: { [key: string]: string } = {
  'Electrocardiogram (ECG or EKG)': 'electrocardiogram (ECG or EKG)',
  'Treadmill Test (TMT)': 'treadmill test (TMT)',
  '2D Echocardiogram': '2D echocardiogram',
  'Complete Blood Count (CBC)': 'complete blood count (CBC)',
  'Thyroid Test': 'thyroid screening test',
  'Allergy Test': 'allergy screening test',
};

export default function PreventativeScreeningEntryList({
  masterLinkId,
  masterScreeningType,
  screeningName,
  infoDescription,
  emptyStateImageSrc,
  hasExternalReports = true,
  action,
  subActive,
}: {
  masterLinkId: any;
  masterScreeningType: MasterScreening;
  screeningName: string;
  infoDescription: string;
  emptyStateImageSrc: string;
  hasExternalReports?: boolean;
  action: string;
  subActive: string;
}) {
  const toast = useToast();
  const navigate = useNavigate();
  const screeningModal = useDisclosure();
  const { PROFILE, EHR, PREVENTATIVE_SCREENING } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;

  const [selectedScreeningInstance, setSelectedScreeningInstance] = useState<Procedure | null>(null);

  const { authenticatedUser } = useAuthService();

  const { preventativeScreeningList } = useGetPrevenativeScreening(
    authenticatedUser?.id,
    masterScreeningType?.value ? findIdentifiersByType(masterScreeningType.value) : []
  );

  const { deletePreventativeScreening } = usePreventativeScreening(authenticatedUser?.id);

  const { trackEventInFlow } = useAnalyticsService();
  const { isPublicMode } = usePublicSettings();

  const onAddHandler = () => {
    setSelectedScreeningInstance(null);
    recordPreventativeScreeningEvents(trackEventInFlow, {
      EventName: 'PreventativeScreeningAddStarted',
      ps_entry_points: 'my_health_profile',
      ps_type: screeningName,
    });
    navigate(`/${PROFILE}/${EHR}/${PREVENTATIVE_SCREENING}/${subActive}/${ADD}`);
  };

  const onEditHandler = (screening: Procedure) => {
    setSelectedScreeningInstance(screening);
    screeningModal.onOpen();
  };

  const onPreviousHandler = () => {
    screeningModal.onClose();
  };

  const onRemoveHandler = async (screening: any) => {
    try {
      if (!screening) return;
      await deletePreventativeScreening(screening.id);
      recordPreventativeScreeningEvents(trackEventInFlow, {
        EventName: 'PreventativeScreeningRemoved',
        ps_entry_points: 'my_health_profile',
        ps_type: screeningName,
        ps_family_history: false,
      });
      toast({
        title: 'Success',
        description: `${screeningName} removed successfully.`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Something went wrong. Please try again later.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  useEffect(() => {
    if (action === ADD) {
      screeningModal.onOpen();
    }
  }, [action]);

  const closeFn = () => {
    screeningModal.onClose();
    if (action === ADD) navigate(NavigationHelper.getEhrView(false, 'preventative-screening', subActive));
  };

  return (
    <>
      <Modal
        variant={MODAL_VARIANTS.PERIWINKLE}
        title={`${screeningName}`}
        showModalFooter={false}
        isCentered
        {...screeningModal}
        onClose={closeFn}
      >
        <Suspense fallback={<FormSkeleton />}>
          <Form
            screening={selectedScreeningInstance}
            masterLinkId={masterLinkId}
            masterScreeningType={masterScreeningType}
            onClose={closeFn}
            familyHistory={null}
            hasPrevious={false}
            onPrevious={onPreviousHandler}
            hasExternalReports={hasExternalReports}
          />
        </Suspense>
      </Modal>
      {Object.values(preventativeScreeningList as Record<string, any[]>)[0].length === 0 ? (
        <SidebarEmptyState
          actionButtonText="Add"
          title={`Update ${
            screeningMapping[screeningName as keyof typeof screeningMapping] || screeningName.toLowerCase()
          } results`}
          imageSrc={emptyStateImageSrc}
          {...(isPublicMode ? { hideActionButton: true } : { onClick: onAddHandler })}
          isPublicMode={isPublicMode}
        />
      ) : (
        <>
          {!isPublicMode && <SidebarAddButton onClick={onAddHandler}>Add</SidebarAddButton>}
          <Stack
            gap="2"
            pt="5"
            height="full"
            overflowY="scroll"
            className="hide-scrollbar"
          >
            {Object.values(preventativeScreeningList as Record<string, any[]>)[0].map((answer: any) => (
              <ScreeningCard
                key={answer.id}
                title={screeningName}
                createdAt={answer.performedDateTime}
                onRemove={() => onRemoveHandler(answer)}
                onEdit={() => onEditHandler(answer)}
                isPublicMode={isPublicMode}
              >
                {answer?.report?.length && (
                  <Flex
                    direction="column"
                    gap="2px"
                  >
                    <LinkedDocumentsLabel />
                    <LinkedDocumentsCard records={useExtractDocumentResource(answer?.report)} />
                  </Flex>
                )}
              </ScreeningCard>
            ))}
          </Stack>
        </>
      )}
      <Spacer />
      {!isPublicMode && (
        <SidebarHelperTooltip
          text={`What is ${addArticle(screeningMapping[screeningName] || screeningName.toLowerCase()).replace(
            'screening ',
            ''
          )}?`}
          tooltipText={infoDescription}
        />
      )}
    </>
  );
}
