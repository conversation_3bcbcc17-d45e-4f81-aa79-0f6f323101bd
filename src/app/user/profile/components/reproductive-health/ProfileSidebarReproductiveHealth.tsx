import React, { Suspense } from 'react';
import { Container, Flex, Stack, Text } from '@chakra-ui/react';

import { ISidebarProps } from '@lib/models/misc';
import { FluentHealthLoader } from '../../../../../components/FluentHealthLoader';
import { QuestionList } from './QuestionList';
import { SidebarCloseButton, SidebarHeading } from '../SidebarComponents';

export function ProfileSidebarReproductiveHealth({ onClose }: ISidebarProps) {
  return (
    <Container
      position="relative"
      height="full"
      overflowY="scroll"
      className="hide-scrollbar"
    >
      <Stack
        py="4"
        height="full"
      >
        <Flex justifyContent="space-between">
          <SidebarHeading>Reproductive Health</SidebarHeading>
          <SidebarCloseButton onClick={onClose} />
        </Flex>
        <Text
          color="charcoal.60"
          fontSize="14px"
          style={{ marginBottom: '16px' }}
        >
          Some of the fields shown may not be applicable to you. Respond to the ones that apply to your sex assigned at
          birth.
        </Text>
        <Suspense fallback={<FluentHealthLoader />}>
          <QuestionList />
        </Suspense>
      </Stack>
    </Container>
  );
}
