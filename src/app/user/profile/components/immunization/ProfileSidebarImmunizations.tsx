import React, { Suspense, lazy, useEffect, useState } from 'react';
import { ChevronLeft as ChevronLeftIcon } from 'react-feather';
import { Box, Card, Container, Flex, Stack, VStack, useTheme } from '@chakra-ui/react';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import { medplumApi } from '@user/lib/medplum-api';
import { useImmunization } from '@user/lib/medplum-state';

import { NavigationHelper, ROUTE_ACTIONS, ROUTE_VARIABLES } from '../../../lib/constants';
import { SidebarCloseButton, SidebarHeading } from '../SidebarComponents';
import { SidebarMenuItem } from '../SidebarMenuItem';
import { ISidebarProps } from '@lib/models/misc';
import { FluentHealthLoader } from 'src/components/FluentHealthLoader';
import { useAuthService, usePublicSettings } from '@lib/state';
import { FACT_CODE_SYSTEM } from '@lib/constants';

const VaccineWithOutGlobalQuestionnaire = lazy(() => import('./VaccineWithOutGlobalQuestionnaire'));

const vaccineComponentConfig: Record<
  string,
  {
    emptyStateImageSrc: string;
    infoDescription: string;
    answerListKey: string;
    answerLinkId: string;
  }
> = {
  covid19: {
    emptyStateImageSrc: '/empty-card-covid-19.png',
    infoDescription:
      'Some vaccinations require multiple doses to be fully effective. Please check with your doctor for more details.',
    answerListKey: 'ImmunizationCovid19',
    answerLinkId: 'immunization-covid-19-date-immunization',
  },
  flu: {
    emptyStateImageSrc: '/empty-card-flu.png',
    infoDescription:
      'Some vaccinations require multiple doses to be fully effective. Please check with your doctor for more details.',
    answerListKey: 'ImmunizationFlu',
    answerLinkId: 'immunization-flu-date-immunization',
  },
  hpv: {
    emptyStateImageSrc: '/empty-card-hpv.png',
    infoDescription:
      'Some vaccinations require multiple doses to be fully effective. Please check with your doctor for more details.',
    answerListKey: 'ImmunizationHPV',
    answerLinkId: 'immunization-hpv-date-immunization',
  },
  hepa: {
    emptyStateImageSrc: '/empty-card-hep-A.png',
    infoDescription:
      'The Mayo Clinic explains that the hepatitis A vaccine prevents infection caused by the hepatitis A virus (HAV). Check with your doctor on the latest Hepatitis A vaccine protocol. Report to a doctor any allergies or conditions that could contribute to adverse reactions.',
    answerListKey: 'ImmunizationHepatitisA',
    answerLinkId: 'immunization-hepatitis-a-date-immunization',
  },
  hepb: {
    emptyStateImageSrc: '/empty-card-hep-b.png',
    infoDescription:
      'The Mayo Clinic explains that the Hepatitis B vaccine is used to prevent infection by the hepatitis B virus. It works by causing your body to produce antibodies against the disease. Report to a doctor any allergies or conditions that could contribute to adverse reactions.',
    answerListKey: 'ImmunizationHepatitisB',
    answerLinkId: 'immunization-hepatitis-b-date-immunization',
  },
  mmr: {
    emptyStateImageSrc: '/empty-card-MMR.png',
    infoDescription:
      'The Indian Academy of Pediatrics describes measles as a generalized rash with high fever, which may predispose a child to pneumonia, ear infection, or tuberculosis. MMR vaccine protects against measles, mumps, and rubella. Report to a doctor any allergies or conditions that could contribute to adverse reactions.',
    answerListKey: 'ImmunizationMeaslesMumpsRubellaMMR',
    answerLinkId: 'immunization-measles-mumps-rubella-mmr-date-immunization',
  },
  chickenpox: {
    emptyStateImageSrc: '/empty-card-chickenpox.png',
    infoDescription:
      'Indian Academy of Paediatrics describes varicella as a highly contagious disease. In the absence of vaccination, it is likely to affect almost all persons. Generally, chickenpox is mild and does not require any treatment but it may be of a serious nature in neonates, very young infants, pregnant women, and people with decreased immunity. When it occurs in pregnancy, it may lead to serious developmental abnormalities in the newborn. Check with your doctor on the latest varicella vaccine protocol, especially if you have never had chickenpox, the varicella vaccine or are exposed to vulnerable populations. Report to your doctor any allergies or conditions that could contribute to adverse reactions.',
    answerListKey: 'ImmunizationChickenpox',
    answerLinkId: 'immunization-chickenpox-date-immunization',
  },
  tb: {
    emptyStateImageSrc: '/empty-card-tuberculosis.png',
    infoDescription:
      'Some vaccinations require multiple doses to be fully effective. Please check with your doctor for more details.',
    answerListKey: 'ImmunizationTuberculosis',
    answerLinkId: 'immunization-tuberculosis-date-immunization',
  },
  shingles: {
    emptyStateImageSrc: '/empty-card-shingles.png',
    infoDescription:
      'The Mayo Clinic describes shingles as a viral infection that causes a painful rash. Shingles can occur anywhere on your body. The shingles vaccine protects against the herpes zoster virus, reducing the risk of shingles and its complications in older adults. Multiple doses may be required. Check with your doctor on the latest vaccine protocol. Report to your doctor any allergies or conditions that could contribute to adverse reactions.',
    answerListKey: 'ImmunizationShingles',
    answerLinkId: 'immunization-shingles-date-immunization',
  },
  tdap: {
    emptyStateImageSrc: '/empty-card-t-dap.png',
    infoDescription:
      'Some vaccinations require multiple doses to be fully effective. Please check with your doctor for more details.',
    answerListKey: 'ImmunizationTDap',
    answerLinkId: 'immunization-t-dap-date-immunization',
  },
  pneumonia: {
    emptyStateImageSrc: '/empty-card-pneumonia.png',
    infoDescription: '',
    answerListKey: 'ImmunizationPneumonia',
    answerLinkId: 'immunization-pneumonia-date-immunization',
  },
};

export default function ProfileSidebarImmunizations({ onClose, action, subActive }: ISidebarProps) {
  const theme = useTheme();
  const navigate = useNavigate();
  const { PROFILE, EHR, VACCINES } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;

  const [immunizations, setImmunizations] = useState<Record<string, any>>({});

  const [currentImmunization, setCurrentImmunization] = useState<any>(null);
  const { authenticatedUser: patient } = useAuthService();
  const { isPublicMode, myPatient } = usePublicSettings();
  const patientId = !isPublicMode ? patient.id : myPatient.id;
  const { immunizationList } = useImmunization(patientId);

  useEffect(() => {
    medplumApi.valueSetList.getAllValueSetFromDirectus(isPublicMode, FACT_CODE_SYSTEM.IMMUNIZATION).then((val: any) => {
      const dynamicImmunizations = val.reduce((acc: any, vaccine: any) => {
        const parts = vaccine.code.split(':');
        const key = parts[1] ? parts[1] : parts[0];
        acc[key] = {
          key,
          code: vaccine.code,
          display: vaccine.display,
          route: key,
        };
        return acc;
      }, {});
      setImmunizations(dynamicImmunizations);
    });
  }, [isPublicMode]);

  const menuItemValues = Object.values(immunizations).reduce((acc, vaccine) => {
    const items = immunizationList?.ImmunizationList || [];
    const matchingItems = items.filter(
      (item: { identifier: { value: any }[] }) => item.identifier?.[0]?.value === vaccine.code
    );
    const timestamp = matchingItems.length ? matchingItems[matchingItems.length - 1].occurrenceDateTime : undefined;
    acc[vaccine.display] = { timestamp };
    return acc;
  }, {} as Record<string, { timestamp?: string }>);

  const handleGoBack = () => {
    if (!currentImmunization) return;
    setCurrentImmunization(null);
    navigate(NavigationHelper.getEhrView(false, 'vaccines'));
  };

  useEffect(() => {
    if (!subActive) return;
    const category = Object.values(immunizations).find((vaccine: any) => vaccine.route === subActive);
    if (category) {
      setCurrentImmunization(category);
    }
  }, [subActive, immunizations]);

  return (
    <Container
      position="relative"
      height="full"
      overflowY="scroll"
      overflowX="hidden"
      className="hide-scrollbar"
    >
      <Stack
        py="4"
        height="full"
      >
        <Flex justifyContent="space-between">
          <Flex
            gap="8px"
            alignItems="center"
            cursor="pointer"
            onClick={handleGoBack}
          >
            {currentImmunization && (
              <ChevronLeftIcon
                size={24}
                color={theme.colors.fluentHealthText[100]}
              />
            )}
            <SidebarHeading>{currentImmunization ? currentImmunization.display : 'Vaccines'}</SidebarHeading>
          </Flex>
          <SidebarCloseButton onClick={onClose} />
        </Flex>

        {!currentImmunization && (
          <Box>
            <Card
              bgColor="white"
              borderRadius="xl"
              borderWidth="1px"
              borderStyle="solid"
              borderColor="periwinkle.400"
              boxShadow="0px 1px 4px rgba(73, 90, 228, 0.12)"
              gap="0"
              w="full"
              mt="16px"
            >
              <VStack
                alignItems="left"
                p="2"
                spacing={1}
              >
                {Object.values(immunizations).map((vaccine: any) => (
                  <React.Fragment key={vaccine.key}>
                    <SidebarMenuItem
                      title={vaccine.display}
                      rightLabel={
                        menuItemValues[vaccine.display]?.timestamp &&
                        dayjs(menuItemValues[vaccine.display]?.timestamp).isValid()
                          ? dayjs(menuItemValues[vaccine.display]?.timestamp).format('DD.MM.YYYY')
                          : undefined
                      }
                      onClick={() => {
                        setCurrentImmunization(vaccine);
                        navigate(`/${PROFILE}/${EHR}/${VACCINES}/${vaccine.route}/${VIEW}`);
                      }}
                    />
                  </React.Fragment>
                ))}
              </VStack>
            </Card>
          </Box>
        )}
        {currentImmunization && (
          <Suspense fallback={<FluentHealthLoader />}>
            <VaccineWithOutGlobalQuestionnaire
              masterScreeningType={currentImmunization}
              screeningName={currentImmunization.display}
              emptyStateImageSrc={vaccineComponentConfig[currentImmunization.key]?.emptyStateImageSrc}
              infoDescription={vaccineComponentConfig[currentImmunization.key]?.infoDescription}
              action={action ?? VIEW}
              subActive={subActive ?? ''}
            />
          </Suspense>
        )}
      </Stack>
    </Container>
  );
}
