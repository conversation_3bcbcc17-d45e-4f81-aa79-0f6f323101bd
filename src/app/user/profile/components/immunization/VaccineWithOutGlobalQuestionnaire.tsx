// TODO Remove this nested ternary
/* eslint-disable no-nested-ternary */
import { Flex, Spacer, Stack, useDisclosure, useToast } from '@chakra-ui/react';
import React, { Suspense, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { NavigationHelper, ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';
import { recordVaccineEvents } from '@user/lib/events-analytics-manager';
import dayjs from 'dayjs';

import { FormSkeleton } from '../../../../../components/ui/Form';
import { MODAL_VARIANTS, Modal } from '../../../../../components/Modal';
import { SidebarEmptyState } from '../SidebarEmptyState';
import { MasterScreening } from '@lib/models/screening';
import { Form } from './Form';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { ScreeningCard } from './ScreeningCard';
import { CardPerformedLabel, SidebarAddButton, SidebarHelperTooltip } from '../SidebarComponents';
import { useImmunization } from 'src/app/user/lib/medplum-state';
import { QuestionnaireResponse } from 'src/gql/graphql';
import { LinkedDocumentsCard, LinkedDocumentsLabel } from '../LinkedDocumentsCard';
import { useExtractDocumentResource } from 'src/app/medical-records/lib/state';

export default function PreventativeScreeningEntryList({
  masterScreeningType,
  screeningName,
  infoDescription,
  emptyStateImageSrc,
  hasExternalReports = true,
  action,
  subActive,
}: {
  masterScreeningType: MasterScreening;
  screeningName: string;
  infoDescription: string;
  emptyStateImageSrc: string;
  hasExternalReports?: boolean;
  action: string;
  subActive: string;
}) {
  const toast = useToast();
  const navigate = useNavigate();
  const screeningModal = useDisclosure();
  const { PROFILE, EHR, VACCINES } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;

  const [selectedScreeningInstance, setSelectedScreeningInstance] = useState(null);

  const { authenticatedUser } = useAuthService();
  const { immunizationList: Immunization, deleteImmunization } = useImmunization(authenticatedUser?.id);
  const filteredImmunizations = Immunization?.ImmunizationList?.filter((item: any) =>
    item.identifier.some((id: any) => id.value === masterScreeningType.code)
  );
  const { trackEventInFlow } = useAnalyticsService();
  const { isPublicMode } = usePublicSettings();

  const onAddHandler = () => {
    setSelectedScreeningInstance(null);
    recordVaccineEvents(trackEventInFlow, {
      EventName: 'VaccineAddStarted',
      vc_entry_point: 'my_health_profile',
      vc_type: masterScreeningType?.display,
    });
    navigate(`/${PROFILE}/${EHR}/${VACCINES}/${subActive}/${ADD}`);
  };

  const onEditHandler = (screening: any) => {
    setSelectedScreeningInstance(screening);
    screeningModal.onOpen();
    recordVaccineEvents(trackEventInFlow, {
      EventName: 'VaccineInteracted',
      vc_entry_point: 'my_health_profile',
      vc_type: masterScreeningType?.display,
    });
  };

  const onPreviousHandler = () => {
    screeningModal.onClose();
    navigate(NavigationHelper.getEhrView(true, VACCINES));
  };

  const onRemoveHandler = async (screening: QuestionnaireResponse) => {
    const deleteTask = 'Delete Vaccine';
    const identifier = 'urn:fh-workflow:task:delete:vaccine';
    const payload: any = {
      immunizationId: screening!.id,
      deleteTask,
      identifier,
    };
    try {
      await deleteImmunization(payload);
      recordVaccineEvents(trackEventInFlow, {
        EventName: 'VaccineRemoved',
        vc_entry_point: 'my_health_profile',
        vc_type: masterScreeningType?.display,
      });

      toast({
        title: 'Success',
        description: `${screeningName} removed successfully.`,
        status: 'success',
        duration: 4000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Something went wrong. Please try again later.',
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    }
  };

  useEffect(() => {
    if (action === ADD) {
      screeningModal.onOpen();
    }
  }, [action]);

  const closeFn = () => {
    screeningModal.onClose();
    if (action === ADD) navigate(NavigationHelper.getEhrView(false, 'vaccines', subActive));
  };

  return (
    <>
      <Modal
        variant={MODAL_VARIANTS.PERIWINKLE}
        title={`${screeningName}`}
        showModalFooter={false}
        isCentered
        {...screeningModal}
        onClose={closeFn}
      >
        <Suspense fallback={<FormSkeleton />}>
          <Form
            screening={selectedScreeningInstance}
            masterScreeningType={masterScreeningType}
            onClose={closeFn}
            hasPrevious={false}
            onPrevious={onPreviousHandler}
            hasExternalReports={hasExternalReports}
            link={subActive}
          />
        </Suspense>
      </Modal>
      {filteredImmunizations.length === 0 ? (
        <SidebarEmptyState
          actionButtonText="Add"
          title={`Update ${screeningName.toLowerCase()} details`}
          imageSrc={emptyStateImageSrc}
          {...(isPublicMode ? { hideActionButton: true } : { onClick: onAddHandler })}
          isPublicMode={isPublicMode}
        />
      ) : (
        <>
          {!isPublicMode && <SidebarAddButton onClick={onAddHandler}>Add</SidebarAddButton>}
          <Stack
            gap="2"
            pt="5"
            height="full"
            overflowY="scroll"
            className="hide-scrollbar"
          >
            {filteredImmunizations.map((answer: any) => (
              <ScreeningCard
                key={answer.id}
                title={screeningName}
                createdAt={`Dose ${answer?.protocolApplied[0]?.doseNumberPositiveInt}`}
                onRemove={() => onRemoveHandler(answer)}
                onEdit={() => onEditHandler(answer)}
                isPublicMode={isPublicMode}
              >
                {answer?.occurrenceDateTime && (
                  <CardPerformedLabel mt="-14px">
                    {dayjs(answer?.occurrenceDateTime).format('DD/MM/YYYY')}
                  </CardPerformedLabel>
                )}
                {answer?.extension?.length && (
                  <Flex
                    direction="column"
                    gap="2px"
                  >
                    <LinkedDocumentsLabel />
                    <LinkedDocumentsCard records={useExtractDocumentResource(answer?.extension)} />
                  </Flex>
                )}
              </ScreeningCard>
            ))}
          </Stack>
        </>
      )}
      <Spacer />
      {!isPublicMode && (
        <SidebarHelperTooltip
          text={`What is the ${screeningName.toLowerCase()}?`}
          tooltipText={infoDescription}
        />
      )}
    </>
  );
}
