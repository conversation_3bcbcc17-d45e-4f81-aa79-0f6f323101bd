import { Suspense, useEffect } from 'react';
import { Flex } from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { ProfileCardSkeleton } from '../components/ProfileCardSkeleton';
import { ProfileBioCard } from '../components/ProfileBioCard';
import ProfileSidebarCTAs from '../components/ProfileSidebarCTAs';

export function HealthProfilePage() {
  const navigate = useNavigate();
  const { PROFILE, BASIC } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;
  useEffect(() => {
    if (window.innerWidth > 992) {
      navigate(`/${PROFILE}/${BASIC}/${VIEW}`);
    }
  }, []);
  return (
    <Suspense fallback={<ProfileCardSkeleton title="Family Members" />}>
      <Flex
        direction="column"
        gap="20px"
        w="full"
      >
        <ProfileBioCard />
        <ProfileSidebarCTAs />
      </Flex>
    </Suspense>
  );
}
