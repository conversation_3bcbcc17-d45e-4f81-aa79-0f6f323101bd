import React, { useState } from 'react';
import { Button, Flex, Stack, Text, useToast } from '@chakra-ui/react';
import { recordSettingsEvents } from '@user/lib/events-analytics-manager';

import { GenericSettingsPage } from './GenericSettingsPage';
import { downloadEntireData } from '../lib/medplum-api';
import { useAnalyticsService, useAuthService } from '@lib/state';
import { downloadIdentifier } from '@lib/constants';

export function DownloadPersonalDataPage() {
  const [isDownloading, setIsDownloading] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const { authenticatedUser } = useAuthService();
  const patientId = authenticatedUser?.id;
  const toast = useToast();

  const { trackEventInFlow } = useAnalyticsService();

  // const beforeDownloadRequestText =
  //   'Clicking on the button below will allow you to download a copy of all your records and data. You will receive this in your personal email account.';

  // const afterDownloadRequestText =
  //   'Your personal data has been emailed to you. If you do not receive it in the next 5 minutes, please contact Fluent Support.';

  const handleDownload = async () => {
    setLoading(true);
    const payload = {
      type: 'transaction',
      resourceType: 'Bundle',
      entry: [
        {
          request: {
            url: 'Task',
            method: 'POST',
          },
          resource: {
            resourceType: 'Task',
            status: 'requested',
            intent: 'option',
            priority: 'routine',
            description: 'Exporting Patient Data',
            input: [
              {
                type: {
                  coding: [
                    {
                      code: downloadIdentifier,
                      display: 'Download Patient Data',
                    },
                  ],
                },
                valueReference: {
                  reference: `Patient/${patientId}`,
                },
              },
            ],
            for: {
              type: 'Patient',
              reference: `Patient/${patientId}`,
            },
            identifier: [
              {
                value: downloadIdentifier,
              },
            ],
          },
        },
      ],
    };
    try {
      const { status } = await downloadEntireData.createDownloadData(payload);

      if (status === 200) {
        setIsDownloading(true);
      } else {
        toast({
          title: 'Something went wrong! Try again later.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (err) {
      toast({
        title: (err as any).message,
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    } finally {
      setLoading(false);

      recordSettingsEvents(trackEventInFlow, {
        EventName: 'PersonalDataDownloaded',
      });
    }

    // eslint-disable-next-line no-promise-executor-return
    // await new Promise((resolve) => setTimeout(resolve, 2000)); // Simulate a 2-second delay
  };

  return (
    <GenericSettingsPage
      title="Download Personal Data"
      width="100%"
      maxWidth="800px"
    >
      <Stack
        padding="20px"
        spacing="16px"
      >
        <Flex
          flexDirection="column"
          alignItems="flex-start"
          justifyContent="flex-start"
          gap={5}
        >
          <Text
            fontSize="lg"
            color="fluentHealthText.100"
          >
            {isDownloading ? 'Email sent successfully' : 'Get your data on email'}
          </Text>
          {isDownloading ? (
            <Text
              fontSize="md"
              color="fluentHealthText.200"
            >
              Your personal data has been emailed to you. If you do not receive it in the next 5 minutes, please contact{' '}
              <a
                href="mailto:<EMAIL>"
                target="_top"
                style={{
                  transition: 'all .3s ease',
                  color: '#495AE4',
                }}
              >
                Fluent Support
              </a>
              .
            </Text>
          ) : (
            <Text
              fontSize="md"
              color="fluentHealthText.200"
            >
              Clicking on the button below will allow you to download a copy of all your records and data. You will
              receive this in your personal email account.
            </Text>
          )}
          {!isDownloading && (
            <Button
              bg="fluentHealth.500"
              color="white"
              variant="quietPrimary"
              height="auto"
              padding="8px 16px"
              marginTop="10px"
              isLoading={isLoading}
              // _hover={{
              //   bg: 'white',
              //   color: 'fluentHealth.500',
              // }}
              onClick={handleDownload}
            >
              Download
            </Button>
          )}
        </Flex>
      </Stack>
    </GenericSettingsPage>
  );
}
