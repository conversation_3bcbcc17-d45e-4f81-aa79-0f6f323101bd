import { Flex, Text, useTheme, useToast } from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { ArrowRight } from 'react-feather';
import { recordSettingsEvents } from '@user/lib/events-analytics-manager';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { GenericSettingsPage } from './GenericSettingsPage';
import { useFetchAppSettingData } from '@lib/contentLibrary/hooks/useFetchAppSettingData';
import { useAnalyticsService } from '@lib/state';

// const texts = LEGAL_LINKS.map((tab) => tab.text);

// const getRouteFromTitle = (title: string) => {
//   const match = findMostMatchingString(texts, title);
//   if (!match) {
//     return null;
//   }
//   return LEGAL_LINKS.find((tab) => tab.title === match)?.route as string;
// };

export function LegalPage() {
  const navigate = useNavigate();
  const theme = useTheme();
  const { data: appSettingData, error: appSettingError } = useFetchAppSettingData();
  const toast = useToast();
  const { trackEventInFlow } = useAnalyticsService();
  if (appSettingError) {
    toast({
      title: 'Something went wrong! Try again later.',
      status: 'error',
      duration: 3000,
      isClosable: true,
    });
  }
  const {
    section_legal_title: title,
    section_legal_terms_title,
    section_legal_privacy_title,
    section_legal_cookie_title,
    section_legal_consent_title,
    section_legal_medadvice_title,
  } = appSettingData || {};

  const {
    TERMS_N_CONDITIONS,
    SETTINGS,
    LEGAL,
    PRIVACY_POLICY,
    COOKIE_POLICY,
    COMMUNICATIONS_CONSENT,
    MEDICAL_ADVICE_DISCLAIMER,
  } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;

  const LEGAL_LINKS = [
    {
      text: 'terms',
      title: section_legal_terms_title,
      route: `/${TERMS_N_CONDITIONS}/${VIEW}`,
    },
    {
      text: 'privacy',
      title: section_legal_privacy_title,
      route: `/${SETTINGS}/${LEGAL}/${PRIVACY_POLICY}/${VIEW}`,
    },
    {
      text: 'cookie',
      title: section_legal_cookie_title,
      route: `/${SETTINGS}/${LEGAL}/${COOKIE_POLICY}/${VIEW}`,
    },
    {
      text: 'consent',
      title: section_legal_consent_title,
      route: `/${SETTINGS}/${LEGAL}/${COMMUNICATIONS_CONSENT}/${VIEW}`,
    },
    {
      text: 'medadvice',
      title: section_legal_medadvice_title,
      route: `/${SETTINGS}/${LEGAL}/${MEDICAL_ADVICE_DISCLAIMER}/${VIEW}`,
    },
  ];
  return (
    <GenericSettingsPage
      title={title}
      width="100%"
      maxWidth="800px"
    >
      <>
        {LEGAL_LINKS.map((ll) => {
          return (
            <Flex
              borderRadius="lg"
              flexDirection="row"
              justifyContent="space-between"
              py="11px"
              px="8px"
              _hover={{ bg: 'periwinkle.200' }}
              cursor="pointer"
              key={ll.title}
              onClick={() => {
                recordSettingsEvents(trackEventInFlow, {
                  EventName: 'LegalPolicyInteracted',
                  st_policy_name: ll.title,
                });
                navigate(ll.route);
              }}
            >
              <Text fontWeight="500">{ll.title}</Text>
              <ArrowRight
                size={20}
                color={theme.colors.papaya[600]}
              />
            </Flex>
          );
        })}
      </>
    </GenericSettingsPage>
  );
}
