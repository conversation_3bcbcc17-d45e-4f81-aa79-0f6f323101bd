import { <PERSON>lex, FormControl, Spacer, Stack, Switch, Text, useTheme, useToast } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { usePatient, usePatientSettingsAll } from '@user/lib/medplum-state';
import { recordSettingsEvents } from '@user/lib/events-analytics-manager';

import { GenericSettingsPage } from './GenericSettingsPage';
import { useAnalyticsService, useAuthService } from '@lib/state';
import { Patient } from '@lib/models/patient';
import { useFetchAppSettingData } from '@lib/contentLibrary/hooks/useFetchAppSettingData';

type Extension = {
  url: string;
  extension?: Extension[];
  valueBoolean?: boolean;
};

// const updateCommunicationPreference = async (label: string, enabled: boolean) => {
//   console.log(`Updating ${label} to ${enabled}`);
//   // eslint-disable-next-line no-promise-executor-return
//   return new Promise((resolve) => setTimeout(resolve, 1000)); // Simulating network delay
// };

// Reusable component for a communication option
function CommunicationOption({ label, type, patient }: { label: string; type: string; patient: Patient }) {
  const theme = useTheme();
  const [isEnabled, setIsEnabled] = useState(false);
  const [loading, setLoading] = useState(false);
  const { trackEventInFlow } = useAnalyticsService();
  const { data, updateBasicSettingsCommunication } = usePatientSettingsAll(patient?.id);
  const communicationPayloadurl =
    (type === 'reminders' ? 'communicationReminders' : 'communicationMarketing') +
    (label === 'In Browser' ? 'Browser' : label === 'Via Email' ? 'Email' : 'SMS');
  useEffect(() => {
    function getPreferenceValue(extensions: Extension[] | undefined, key: string): boolean | undefined {
      return extensions?.find((item) => item.url === key)?.extension?.find((ext) => ext.url === 'preference')
        ?.valueBoolean;
    }

    const preferenceValue = getPreferenceValue(
      data?.extension?.[0]?.extension?.[1]?.extension?.[2]?.extension,
      communicationPayloadurl
    );

    setIsEnabled(preferenceValue ?? false);
  }, []);

  const handleToggle = async (selectedlabel: string, selectedtype: string) => {
    setLoading(true);
    const newStatus = !isEnabled;

    try {
      await updateBasicSettingsCommunication({ value: newStatus, url: communicationPayloadurl, id: data?.id });
      recordSettingsEvents(trackEventInFlow, {
        EventName:
          selectedtype === 'reminders'
            ? 'CommunicationPreferencesRemindersInteracted'
            : 'CommunicationPreferencesMarkComInteracted',
        ...(selectedlabel === 'In Browser' && { st_push_notifications_preference: newStatus }),
        ...(selectedlabel === 'Via Email' && { st_email_preference: newStatus }),
        ...(selectedlabel === 'Via Sms' && { st_whatsapp_preference: newStatus }),
      });
      setIsEnabled(newStatus);
    } catch (error) {
      console.error(`Failed to update ${label}:`, error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Flex alignItems="center">
      <Text
        fontWeight="500"
        fontSize="lg"
        maxWidth="512px"
      >
        {label}
      </Text>
      <Spacer />
      <FormControl
        width="28px"
        mr="2"
        ml="16"
      >
        <Switch
          size="md"
          color={theme.colors.fluentHealth[500]}
          id={`${type} ${label}`.replace(/\s+/g, '-').toLowerCase()}
          isChecked={isEnabled}
          onChange={() => handleToggle(label, type)}
          isDisabled={loading} // Disable the switch while the API call is in progress
        />
      </FormControl>
    </Flex>
  );
}

// Reusable component for a communication section (like "Reminders" or "Marketing Communication")
function CommunicationSection({ title, options, patient }: { title: string; options: string[]; patient: Patient }) {
  return (
    <Stack padding="12px 12px">
      <Text
        fontSize="20px"
        fontWeight="400"
      >
        {title}
      </Text>
      {options.map((option) => (
        <CommunicationOption
          key={title + option}
          label={option}
          type={title.toLowerCase()}
          patient={patient}
        />
      ))}
    </Stack>
  );
}

export function CommunicationPreferencePage() {
  const { authenticatedUser } = useAuthService();
  const { patient } = usePatient(authenticatedUser?.id);
  const { data: appSettingData, error: appSettingError } = useFetchAppSettingData();
  const toast = useToast();
  if (appSettingError) {
    toast({
      title: 'Something went wrong! Try again later.',
      status: 'error',
      duration: 3000,
      isClosable: true,
    });
  }
  const { section_communication_preferences_title: title } = appSettingData || {};
  return (
    <GenericSettingsPage
      title={title}
      width="100%"
      maxWidth="800px"
    >
      <>
        {/* TODO: Reminders Notification - Will be pick after Launch */}
        <CommunicationSection
          title="Marketing Communication"
          options={['In Browser', 'Via Email', 'Via SMS']}
          patient={patient}
        />
      </>
    </GenericSettingsPage>
  );
}
