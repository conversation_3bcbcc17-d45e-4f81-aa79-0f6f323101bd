import { Box, Button, ChakraProps, Text } from '@chakra-ui/react';
import { recordAddnewRecordsEvents } from '@user/lib/events-analytics-manager';

import { StepHeading } from './StepComponents';
import { useAnalyticsService } from '@lib/state';

import { ReactComponent as SuccessStatusIcon } from '@assets/objects/success-like.svg';

export function Circle(props: ChakraProps) {
  return (
    <Box
      position="fixed"
      zIndex={-1}
      width="1576px"
      height="1576px"
      border="1px solid"
      borderColor="iris.500"
      borderRadius="full"
      opacity={0.2}
      {...props}
    />
  );
}

export function SuccessStatusStep({
  onAddMoreRecords,
  // onSetReminder,
  onSeeRecordsInReview,
  isSidebarOpen = false,
}: {
  onAddMoreRecords: Function;
  // onSetReminder: Function;
  onSeeRecordsInReview: Function;
  isSidebarOpen?: boolean;
}) {
  const { trackEventInFlow } = useAnalyticsService();
  return (
    <>
      <Circle
        bottom="35%"
        left="166px"
      />
      <Circle
        top="20%"
        right="166px"
        width="1774px"
        height="1774px"
      />
      <SuccessStatusIcon style={{ marginBottom: '20px', flexShrink: 0 }} />
      <Box mb="48px">
        <StepHeading textAlign="center">
          Your records have been
          <br />
          submitted for processing
        </StepHeading>
        <Text
          mb="16px"
          fontSize="lg"
          maxW="325px"
          textAlign="center"
        >
          We will notify you when they’re ready.
          <br />
          You can add more in the meantime.
        </Text>
      </Box>
      {isSidebarOpen ? (
        <Button
          size="xl"
          mb="36px"
          onClick={() => onAddMoreRecords()}
        >
          Continue
        </Button>
      ) : (
        <Button
          size="xl"
          mb="36px"
          onClick={() => {
            onAddMoreRecords();
            recordAddnewRecordsEvents(trackEventInFlow, {
              EventName: 'AddNewRecordInteracted',
              ar_entry_point: 'Add record button on submission',
            });
          }}
        >
          Add more records
        </Button>
      )}
      {!isSidebarOpen && (
        <Button
          variant="quiet"
          color="fluentHealth.500"
          size="xl"
          onClick={() => onSeeRecordsInReview()}
        >
          See records in review
        </Button>
      )}
    </>
  );
}
