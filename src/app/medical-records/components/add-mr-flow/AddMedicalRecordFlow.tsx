// Package modules
import {
  Box,
  ChakraProps,
  Fade,
  Flex,
  ModalContent,
  Show,
  Text,
  UseDisclosureReturn,
  useDisclosure,
  useTheme,
  useToast,
} from '@chakra-ui/react';
import { PropsWithChildren, Suspense, useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import shallow from 'zustand/shallow';
import { recordUploadRecordEvents } from '@user/lib/events-analytics-manager';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';
import { medplumApi as medplumApi2 } from '@user/lib/medplum-api';
import * as pdfjsLib from 'pdfjs-dist';

import { useAnalyticsService, useAuthService } from '@lib/state';
import { extractId, useNotificationDisclosure } from '@lib/utils/utils';
import { FluentHealthLoader } from 'src/components/FluentHealthLoader';
import { hexOpacity } from 'src/components/theme/utils';
import { FullscreenModal } from 'src/components/ui/FullscreenModal';
import { CircleWaveDecoration } from '../../../../components/ui/Decorations/CircleWaveDecoration';
import { checkDocumentDuplicate, createTaskPayload, sha1, useStepper } from '../../lib/utils';
import { PrivacyWarning } from './Notifications';
import { ReminderFlow } from '../ReminderFlow';
import { ADD_MEDICAL_RECORD_STEPS, AMRStepperState } from './models';
import { SelectTypeStep } from './SelectTypeStep';
import { SuccessStatusStep } from './SuccessStatusStep';
import { UploadFilesStep } from './UploadFilesStep';
import { medplumApi } from '../../lib/medplum-api';
import { MAX_SIZE_BYTES, MAX_SIZE_MB, convertBytesToMB, documentUploadWorkflow } from '../../lib/constants';
import { useFileUploadStore } from '../../lib/uploadState';
import { getAllDocumentReference, getOrganizationList } from '../../lib/state';

function FixedAndCenteredBox(props: PropsWithChildren & ChakraProps) {
  return (
    <Flex
      position="fixed"
      top="50%"
      left="0"
      right="0"
      transform="translateY(-50%)"
      zIndex="var(--chakra-zIndices-popover)"
      justify="center"
      {...props}
    />
  );
}

// eslint-disable-next-line complexity
function AddMedicalRecordStepper({
  stepperModal,
  isSidebarOpen = false,
  setCurrentStep,
  fetchMedicalRecord,
  total,
}: {
  stepperModal: UseDisclosureReturn;
  isSidebarOpen?: boolean;
  setCurrentStep: (val: number) => void;
  fetchMedicalRecord?: () => void;
  total?: number;
}) {
  const [familyMemberId, setFamilyMemberId] = useState('');
  const [uploadHealthRecordEventDetails, setUploadHealthRecordEventDetails] = useState<any>([]);
  const { DOCUMENTS, UPLOAD } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;
  const stepper = useStepper<AMRStepperState>(Object.keys(ADD_MEDICAL_RECORD_STEPS).length, {});
  const { authenticatedUser } = useAuthService();
  const patientId = authenticatedUser?.id;
  const { medicalRecordList } = getAllDocumentReference(patientId);
  const { trackEventInFlow } = useAnalyticsService();
  const theme = useTheme();
  const toast = useToast();
  const navigate = useNavigate();
  const params = useParams();
  const { active } = params;

  useEffect(() => {
    if (total) {
      stepper.setStep(ADD_MEDICAL_RECORD_STEPS.UPLOAD_FILES);
    }
    switch (active) {
      case UPLOAD:
        setCurrentStep(ADD_MEDICAL_RECORD_STEPS.UPLOAD_FILES);
        break;
      default:
        break;
    }
  }, [active]);
  // Submit handlers
  const newHealthRecordSubmitHandler = useCallback(
    (familyMember: any) => {
      stepper.setStep(ADD_MEDICAL_RECORD_STEPS.UPLOAD_FILES);
      setFamilyMemberId(familyMember?.id);
      recordUploadRecordEvents(trackEventInFlow, {
        EventName: 'UploadRecordInProgUserSelected',
        ur_user: 'others',
      });
      recordUploadRecordEvents(trackEventInFlow, { EventName: 'UploadRecordInProgSuccessful' });
    },
    [stepper]
  );
  const someAsyncOperation = () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve('Operation Complete');
      }, 1000);
    });
  };
  const uploadFilesStepSubmitHandler = async () => {
    stepper.setStep(ADD_MEDICAL_RECORD_STEPS.PROCESSING);
    try {
      await someAsyncOperation();
      stepper.setStep(ADD_MEDICAL_RECORD_STEPS.SUCCESS);
      setCurrentStep(ADD_MEDICAL_RECORD_STEPS.SUCCESS);
      useFileUploadStore.getState().clearFiles();
    } catch (error) {
      console.error('Upload failed:', error);
    }
    uploadHealthRecordEventDetails?.forEach((element: any) => {
      const ur_file_size = convertBytesToMB(element?.ur_file_size);
      recordUploadRecordEvents(trackEventInFlow, {
        EventName: 'UploadRecordCompleted',
        ur_file_size,
        ur_encryption: element?.ur_encryption,
        document_id: extractId(element?.document_id),
      });
    });
  };

  const isPasswordProtected = async (file: File): Promise<boolean> => {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;

      if (pdf.numPages > 0) {
        return false; // If the PDF loads pages, it's not password-protected
      }
    } catch (err: any) {
      if (err?.message.includes('password')) {
        return true; // PDF.js throws an error for password-protected PDFs
      }
    }
    return false;
  };

  const uploadFilesStepChangeHandler = async (files: File[]) => {
    try {
      const fileStore = useFileUploadStore.getState();
      const uploadedFileNames = new Set(Object.keys(fileStore.files));
      const newFiles = files.filter((file) => !uploadedFileNames.has(file.name));

      if (newFiles.length === 0) return;

      const organizationId = await getOrganizationList();

      const uploadFile = async (file: File) => {
        const fileHash = await sha1(file.name);
        const controller = new AbortController();

        // Add file with controller immediately
        fileStore.addFile(file, controller);

        // Check for duplicate or oversize
        if (checkDocumentDuplicate(fileHash, medicalRecordList)) {
          fileStore.setError(file.name, 'File already exists.', false);
          return null;
        }

        if (file.size > MAX_SIZE_BYTES) {
          fileStore.setError(
            file.name,
            `This file exceeds the ${MAX_SIZE_MB} MB limit and it can’t be uploaded.`,
            false
          );
          return null;
        }

        try {
          const passwordProtected = await isPasswordProtected(file);

          const { config, data } = await medplumApi2.uploadFileHandler(
            file,
            (event) => {
              const percent = event.total ? Math.round((event.loaded * 100) / event.total) : 0;
              fileStore.updateProgress(file.name, percent);
            },
            controller.signal
          );

          return { file, fileHash, passwordProtected, config, data };
        } catch (error) {
          if (!controller.signal.aborted) {
            console.error(`Upload failed for ${file.name}:`, error);
            fileStore.setError(file.name, 'File couldn’t be uploaded due to a network issue.', false);
          } else {
            console.warn(`Upload for ${file.name} was cancelled.`);
          }
          return null;
        }
      };

      const uploadResults = await Promise.all(newFiles.map(uploadFile));

      const entries = uploadResults
        .filter((res): res is NonNullable<typeof res> => res !== null)
        .flatMap(({ fileHash, passwordProtected, config, data }) => {
          const documentRefId = crypto.randomUUID();

          const attachment = {
            contentType: data.contentType,
            url: data.url,
            size: config.data.size,
            title: config.data.name,
            hash: fileHash,
            creation: new Date().toISOString(),
          };

          const documentReference: any = {
            resourceType: 'DocumentReference',
            status: 'preliminary',
            subject: { reference: `Patient/${patientId}` },
            content: [{ attachment }],
            ...(familyMemberId && {
              context: {
                related: [{ reference: `FamilyMemberHistory/${familyMemberId}` }],
              },
            }),
            ...(passwordProtected && {
              meta: {
                tag: [
                  {
                    system: 'https://fluentinhealth.com/FHIR/CodeSystem/FluentHealthUI',
                    code: 'password-protected:true',
                    display: 'protected',
                  },
                ],
              },
            }),
          };

          const taskPayload = createTaskPayload(
            passwordProtected
              ? documentUploadWorkflow.passwordSubmission.required
              : documentUploadWorkflow.doucmentUpload.completed,
            'in-progress',
            patientId,
            { reference: `urn:uuid:${documentRefId}` },
            organizationId ? `Organization/${organizationId}` : undefined
          );

          return [
            {
              fullUrl: `urn:uuid:${documentRefId}`,
              resource: documentReference,
              request: { method: 'POST', url: 'DocumentReference' },
            },
            {
              fullUrl: `urn:uuid:${crypto.randomUUID()}`,
              resource: taskPayload,
              request: { method: 'POST', url: 'Task' },
            },
          ];
        });

      if (entries.length > 0) {
        const response = await medplumApi.medicalRecord.addOne({
          resourceType: 'Bundle',
          type: 'transaction',
          entry: entries,
        });
        const uploadHealthRecordDetails = response?.data?.entry
          .filter((entry: any) => entry?.response?.location?.startsWith('DocumentReference/'))
          .map((entry: any) => {
            const resource = entry?.resource;
            const location = entry?.response?.location;

            const ur_file_size = resource?.content?.[0]?.attachment?.size || '';

            const ur_encryption =
              resource?.meta?.tag?.some((tag: any) => tag?.code === 'password-protected:true') || false;
            return {
              document_id: location,
              ur_file_size,
              ur_encryption,
            };
          });
        setUploadHealthRecordEventDetails([...uploadHealthRecordDetails, ...uploadHealthRecordEventDetails]);
        fetchMedicalRecord?.();
      }
    } catch (error: any) {
      console.error(error);
      toast({
        title: error.message || 'Something went wrong!',
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    }
  };

  function onAddMoreRecords() {
    if (isSidebarOpen) {
      stepperModal.onClose();
    } else {
      stepper.updateState({});
      stepper.setStep(ADD_MEDICAL_RECORD_STEPS.SELECT_TYPE);
    }
  }
  const onPreviousHandler = () => {
    stepper.goToPrevStep();
    // if (stepper.currentStep === ADD_MEDICAL_RECORD_STEPS.UPLOAD_FILES) {
    //   navigate(-1);
    // }
  };

  function onSeeRecordsInReview() {
    stepperModal.onClose();
    navigate(`/${DOCUMENTS}/${VIEW}`);
  }

  return (
    <Box>
      {stepper.currentStep !== ADD_MEDICAL_RECORD_STEPS.PROCESSING && (
        <Show above="md">
          <CircleWaveDecoration
            position="fixed"
            bottom="-483px"
            left="-483px"
            width="966px"
            zIndex={-1}
            strokeWidth="0.6"
            color={hexOpacity(theme.colors.fluentHealth[500], 0.2)}
          />
        </Show>
      )}
      <Flex
        position="absolute"
        top="18px"
        left="24px"
        display={{ base: 'block', md: 'none' }}
      >
        <Text
          color="gray.500"
          fontSize="xl"
        >
          Add record
        </Text>
      </Flex>
      {stepper.currentStep === ADD_MEDICAL_RECORD_STEPS.SELECT_TYPE && (
        <Fade in>
          <SelectTypeStep
            initialState={stepper.stepperState[ADD_MEDICAL_RECORD_STEPS.SELECT_TYPE]}
            newHealthRecordSubmitHandler={newHealthRecordSubmitHandler}
            familyMemberId={familyMemberId}
          />
          <PrivacyWarning />
        </Fade>
      )}
      {stepper.currentStep === ADD_MEDICAL_RECORD_STEPS.UPLOAD_FILES && (
        <Fade in>
          <UploadFilesStep
            initialState={stepper.stepperState[ADD_MEDICAL_RECORD_STEPS.UPLOAD_FILES]}
            onSubmit={uploadFilesStepSubmitHandler}
            onChange={uploadFilesStepChangeHandler}
            onPrevious={onPreviousHandler}
          />
        </Fade>
      )}
      {stepper.currentStep === ADD_MEDICAL_RECORD_STEPS.PROCESSING && (
        <Fade in>
          <FixedAndCenteredBox>
            <FluentHealthLoader showHeading={false} />
          </FixedAndCenteredBox>
        </Fade>
      )}
      {stepper.currentStep === ADD_MEDICAL_RECORD_STEPS.SUCCESS && (
        <Fade in>
          <FixedAndCenteredBox
            bg="linear-gradient(0deg, #FFEFD8 0%, #CED1FF 100%)"
            height="100%"
            alignItems="center"
            flexDirection="column"
          >
            <SuccessStatusStep
              onAddMoreRecords={onAddMoreRecords}
              // onSetReminder={onSetReminder}
              onSeeRecordsInReview={onSeeRecordsInReview}
              isSidebarOpen={isSidebarOpen}
            />
          </FixedAndCenteredBox>
        </Fade>
      )}
    </Box>
  );
}

export function AddMedicalRecordFlow({
  stepperModal,
  isSidebarOpen = false,
  fetchMedicalRecord,
}: {
  stepperModal: UseDisclosureReturn;
  isSidebarOpen?: boolean;
  fetchMedicalRecord?: () => void;
}) {
  const [currentStep, setCurrentStep] = useState(0);
  const navigate = useNavigate();
  const disclosure = useNotificationDisclosure((state) => state, shallow);
  // const { trackEventInFlow } = useAnalyticsService();
  const { DOCUMENTS } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;
  const { files: filesData } = useFileUploadStore();
  const fileEntries = Object.values(filesData);
  const total = fileEntries.length;
  const uploaded = fileEntries.filter(({ progress }) => progress === 100).length;

  const reminderModal = useDisclosure();
  const cancelStepperModal = useDisclosure();
  const calendarModal = useDisclosure();
  const clearAsyncOperation = () => {
    return new Promise(() => {
      setTimeout(() => {
        if (total === uploaded) {
          useFileUploadStore.getState().clearFiles();
        }
      }, 10000);
    });
  };
  const onTrackCloseEvent = () => {
    cancelStepperModal.onClose();
    stepperModal.onClose();
    clearAsyncOperation();
    navigate(`/${DOCUMENTS}/${VIEW}`);
  };

  const onRemainderCreated = () => {
    onTrackCloseEvent();
    disclosure.onOpen();
    disclosure.setTabIndex(1);
  };

  return (
    <>
      <FullscreenModal
        background="gradient.loginPage"
        isOpen={stepperModal.isOpen}
        onClose={onTrackCloseEvent}
        isMinimized={currentStep !== ADD_MEDICAL_RECORD_STEPS.SUCCESS}
        onCloseIconClick={onTrackCloseEvent}
      >
        <Suspense
          fallback={
            <FixedAndCenteredBox>
              <FluentHealthLoader />
            </FixedAndCenteredBox>
          }
        >
          <ModalContent
            pb={{ base: '40px', md: '0px' }}
            bg="transparent"
            boxShadow="none"
            width="100%"
            maxW="auto"
            height="100%"
            maxH="100%"
          >
            <AddMedicalRecordStepper
              stepperModal={stepperModal}
              isSidebarOpen={isSidebarOpen}
              setCurrentStep={setCurrentStep}
              total={total}
              fetchMedicalRecord={fetchMedicalRecord}
            />
          </ModalContent>
        </Suspense>
      </FullscreenModal>

      <ReminderFlow
        reminderRecordModal={reminderModal}
        onSuccess={onRemainderCreated}
        calendarModal={calendarModal}
      />
    </>
  );
}
