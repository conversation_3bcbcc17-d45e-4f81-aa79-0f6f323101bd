import { Button, ChakraProps, Flex, Text } from '@chakra-ui/react';
import { <PERSON><PERSON><PERSON><PERSON>andler, ReactNode } from 'react';

import { ReactComponent as EmptyFHLogo } from '@assets/icons/logo-mark.svg';

type ButtonProps = {
  btnLabel: string | ReactNode;
  onAction: MouseEventHandler<HTMLButtonElement>;
  ButtonPrefix?: ReactNode;
  ButtonSuffix?: ReactNode;
};

export function EmptyStateTabsCard({
  title = 'No records found',
  description,
  buttonProps,
  isPublicRecordMode,
  ...props
}: ChakraProps & {
  title?: string | ReactNode;
  description?: string;
  buttonProps?: ButtonProps;
  isPublicRecordMode?: boolean;
}) {
  const { btnLabel, onAction, ...restProps } = buttonProps ?? {};

  return (
    <Flex
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      py="136px"
      maxWidth="1120px"
      {...props}
    >
      <EmptyFHLogo />

      <Text
        fontSize="xl"
        letterSpacing="-0.36px"
        lineHeight="32px"
        color="gray.500"
        textAlign="center"
        maxWidth="320px"
        marginTop="16px"
        // fontFamily="P22 Mackinac" // * P22 Mackinac used in the entire platform is not being reflected anywhere, need to create a seperate ticket
      >
        {title}
      </Text>

      <Text
        fontSize="lg"
        letterSpacing="-0.36px"
        lineHeight="26px"
        color="gray.400"
        textAlign="center"
        maxWidth="600px"
        marginTop="4px"
      >
        {description}
      </Text>

      {btnLabel && !isPublicRecordMode && (
        <Button
          h={{ md: '48px' }}
          borderRadius={{ base: '100%', md: '48px' }}
          bgColor="fluentHealth.500"
          color="fluentHealthSecondary.500"
          fontFamily="Apercu"
          fontWeight="medium"
          transition="all .3s ease"
          padding={{ base: '8px', md: '12px 24px' }}
          minWidth={{ base: '0', md: '3rem' }}
          iconSpacing={{ base: '0px', md: '4px' }}
          onClick={onAction}
          _hover={{
            iconSpacing: '8px',
            borderColor: 'iris.500',
            bgColor: 'royalBlue.600',
          }}
          marginTop="32px"
          {...restProps}
        >
          {btnLabel}
        </Button>
      )}
    </Flex>
  );
}
