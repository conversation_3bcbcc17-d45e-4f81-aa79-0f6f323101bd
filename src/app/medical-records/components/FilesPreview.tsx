import React, { memo } from 'react';
import {
  Box,
  Flex,
  Grid,
  IconButton,
  Image,
  Modal,
  ModalContent,
  ModalOverlay,
  Stack,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import { X as CloseIcon } from 'react-feather';

// import { MedicalRecordContent } from '@lib/models/medical-record';
// import { FluentHealthLoader } from 'src/components/FluentHealthLoader';
import { SidebarHeading } from 'src/app/user/profile/components/SidebarComponents';
import { pluralize } from '@lib/utils/utils';
import { enumContentType, useDocRefDetails } from '../lib/state';

// const useFileInfo = (file: File | MedicalRecordContent) => {
//   const fileInfo: {
//     name: string;
//     url: string;
//     size?: number | null;
//     type: 'application/pdf' | string;
//     data?: string;
//   } = {
//     name: '',
//     url: '',
//     size: null,
//     type: '',
//     data: '',
//   };

//   // If medical record file
//   if ('attachment' in file) {
//     fileInfo.url = file.attachment.url;
//     fileInfo.name = file.attachment.title;
//     fileInfo.type = file.attachment.contentType;
//     fileInfo.data = file.attachment.data;

//     return fileInfo;
//   }

//   // If uploaded file
//   const blob = new Blob([file], { type: file.type });
//   const fileURL = URL.createObjectURL(blob);

//   fileInfo.url = fileURL;
//   fileInfo.name = file.name;
//   fileInfo.size = file.size;
//   fileInfo.type = file.type;

//   return fileInfo;
// };
export const FilePreview = memo(({ file }: { file: any }) => {
  if (!file) return null;
  const { url, title, size, contentType, type, path } = file;

  const openInNewTab = () => {
    if (url) {
      const newWindow = window.open();
      newWindow?.document.write(
        `<iframe src="${url}" frameborder="0" style="border:none; width:100%; height:100%;" allowfullscreen></iframe>`
      );
    }
  };

  const isPDF = contentType === 'application/pdf' || type === 'application/pdf';

  return (
    <Flex
      direction="column"
      justify="center"
      width="180px"
      cursor="pointer"
      onClick={openInNewTab}
    >
      <Flex
        position="relative"
        justify="center"
        align="center"
        width="full"
        height="200px"
        border="1px solid #929CEF"
        bgColor="white"
        borderRadius="4px"
        overflow="hidden"
        mb="8px"
      >
        <Image
          src={isPDF ? '/pdf-document.png' : url || '/pdf-document.png'}
          alt="Medical Record Attachment"
          maxW="full"
          fit="cover"
        />
      </Flex>
      <Text
        align="center"
        overflow="hidden"
        whiteSpace="nowrap"
        textOverflow="ellipsis"
        px="8px"
      >
        {title || path || 'Untitled'}
      </Text>
      {size > 0 && (
        <Text
          align="center"
          fontSize="smaller"
          color="fluentHealthText.200"
          mt="6px"
        >
          {(size / 1000).toFixed(2)} KB
        </Text>
      )}
    </Flex>
  );
});

export function FilesPreview({
  files: allFiles,
  children,
}: {
  files?: NonNullable<ReturnType<typeof useDocRefDetails>['recordAttachments']>;
  children?: (props: any) => React.ReactNode;
}) {
  const filesDrawer = useDisclosure();
  const files = allFiles?.filter(
    (file: any) =>
      file.contentType !== enumContentType.JSON &&
      file.contentType !== enumContentType.DICOM &&
      file.contentType !== enumContentType.PLAINTEXT
  );

  const hoverAnimation: any =
    files && files.length > 1
      ? {
          '& > img:first-of-type': { transform: 'rotate(5deg) scale(1.03)' },
          '& > img:last-of-type': { transform: 'rotate(-5deg) scale(1.03)' },
        }
      : {
          '& > img:first-of-type': { transform: 'scale(1.03)' },
        };

  const ctaElement = children?.({ onClick: filesDrawer.onOpen }) ?? (
    <Box
      position="relative"
      cursor="pointer"
      _hover={hoverAnimation}
      onClick={filesDrawer.onOpen}
    >
      {files && files.length > 1 && (
        <Image
          position="absolute"
          transform="rotate(3deg)"
          src="/pdf-document.png"
          width="160px"
          transition=".2s ease"
        />
      )}
      <Image
        {...(files && files.length > 1 ? { transform: 'rotate(-4deg)' } : {})}
        src="/pdf-document.png"
        width="160px"
        transition=".2s ease"
      />
    </Box>
  );

  return (
    <>
      {ctaElement}
      <Modal
        isOpen={filesDrawer.isOpen}
        onClose={filesDrawer.onClose}
      >
        <Box
          position="relative"
          zIndex="var(--chakra-zIndices-popover)"
          sx={{
            '& .chakra-modal__content-container': {
              justifyContent: 'flex-end',
            },
          }}
        >
          <ModalOverlay />
          <ModalContent
            bgColor="fluentHealthSecondary.500"
            background="gradient.profileDrawer"
            height="full"
            zIndex="var(--chakra-zIndices-skipLink)"
            my="0"
            maxHeight="100%"
            right="0"
            px="22px"
            py="22px"
            maxH="100vh"
          >
            <Stack
              spacing="24px"
              height="full"
            >
              <Flex
                justifyContent="space-between"
                align="center"
              >
                <SidebarHeading>
                  Uploaded {files?.length} {pluralize('file', files?.length!)}
                </SidebarHeading>
                <IconButton
                  aria-label="Close"
                  bgColor="transparent"
                  color="fluentHealthText.300"
                  size="md"
                  p="8px"
                  mt="-22px"
                  mr="-12px"
                  borderRadius="50%"
                  _hover={{
                    color: 'fluentHealthText.100',
                    bgColor: 'fluentHealthSecondary.300',
                  }}
                  onClick={filesDrawer.onClose}
                  icon={<CloseIcon width="20px" />}
                />
              </Flex>
              <Grid
                templateColumns="repeat(2, 1fr)"
                rowGap="24px"
                overflow="auto"
              >
                {files?.map((file: any, index: any) => (
                  <Flex
                    // We don't have a unique property on the uploaded file object, so an index is used.
                    // eslint-disable-next-line react/no-array-index-key
                    key={index}
                    align="flex-start"
                    justify="center"
                  >
                    <FilePreview file={file} />
                  </Flex>
                ))}
              </Grid>
            </Stack>
          </ModalContent>
        </Box>
      </Modal>
    </>
  );
}
