// Package modules
import { useStep } from 'usehooks-ts';
import React, { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';

import { MEDICAL_RECORD_FILTERS, MedicalRecord } from '@lib/models/medical-record';
import { CODE_SYSTEMS_MAPPING, COMM_REMINDER_EXTENSION } from './constants';
import { DocumentReference as DocumentReferenceType, Task, ValueSet } from 'src/gql/graphql';

export interface StepperReturn<T> {
  // State
  stepperState: T;
  updateState: React.Dispatch<React.SetStateAction<T>>;
  // Stepper
  currentStep: number;
  canGoToPrevStep: boolean;
  canGoToNextStep: boolean;
  goToNextStep: () => void;
  goToPrevStep: () => void;
  resetStepper: () => void;
  setStep: Dispatch<SetStateAction<number>>;
}

/**
 * @param steps
 * @param initialState
 */
export const useStepper = <T>(steps: number = 0, initialState: T): StepperReturn<T> => {
  const [state, setState] = useState(initialState);
  const [currentStep, helpers] = useStep(steps);

  const { canGoToPrevStep, canGoToNextStep, goToNextStep, goToPrevStep, reset, setStep } = helpers;

  return {
    // State
    stepperState: state,
    updateState: setState,
    // Stepper
    currentStep,
    canGoToPrevStep,
    canGoToNextStep,
    goToNextStep,
    goToPrevStep,
    resetStepper: reset,
    setStep,
  };
};

/**
 * This hook returns a tuple containing a reference to an HTML element and a ref used to interface with the component.
 * It is used to pass a reference to an HTML element into the Swiper component.
 */
export const useSwiperRef = (): [HTMLElement | null, React.Ref<any> | null] => {
  const [wrapper, setWrapper] = useState(null);
  const ref = useRef(null);

  useEffect(() => {
    setWrapper(ref.current);
  }, []);

  return [wrapper, ref];
};

export const generateArrayOfYears = (start: string = new Date().getFullYear().toString(), end: string = '1900') => {
  const max = new Date(start).getFullYear();
  const min = new Date(end).getFullYear();
  const years = [];

  for (let i = max; i >= min; i--) {
    years.push(i.toString());
  }
  return years;
};

/**
 * Compare two arrays of strings.
 * @param {string[]} arr1 - The first array of strings to compare.
 * @param {string[]} arr2 - The second array of strings to compare.
 * @returns {boolean} True if the arrays contain the same strings (disregarding order), false otherwise.
 */
export const compareArrays = (arr1: string[], arr2: string[]) => {
  // Check if the arrays have the same length
  if (arr1.length !== arr2.length) {
    return false;
  }

  // Sort both arrays
  const sortedArr1 = arr1.slice().sort();
  const sortedArr2 = arr2.slice().sort();

  // Build sorted strings from array elements
  const str1 = sortedArr1.join('');
  const str2 = sortedArr2.join('');

  // Compare the sorted strings
  return str1 === str2;
};

/**
 * Type guard for getting current date from the search params
 * @param dateType
 * @param searchParams
 * @returns new Date() | null
 */
export const getDateSearchParamValue = (
  dateType: MEDICAL_RECORD_FILTERS.FROM_DATE | MEDICAL_RECORD_FILTERS.TO_DATE,
  searchParams: URLSearchParams
): Date | null => {
  const dateValue = searchParams.get(dateType);

  if (dateValue && dateValue.length > 0) {
    return new Date(dateValue);
  }

  return null;
};

/**
 * Formats a list of medical records into a timeline format.
 * @param medicalRecordList - The list of medical records to format.
 * @returns The formatted medical records in a timeline format.
 */

export const formatMedicalRecordListToTimeline = (
  medicalRecordList: MedicalRecord[]
): Array<MedicalRecord & { period: number | null | undefined }> => {
  const sortedList = [...medicalRecordList].reduce((accumulator: any, currentRecord: any) => {
    const year = new Date(currentRecord.date).getFullYear();
    const period = accumulator[year] ? accumulator[year].period : currentRecord.date;
    accumulator[year] = {
      period,
      records: [
        ...(accumulator[year]?.records || []),
        {
          ...currentRecord,
          period: accumulator[year]?.period ? null : period,
        },
      ],
    };
    return accumulator;
  }, {});

  return Object.values(sortedList).flatMap(({ records }: any) => records);
};

export const getRecordTypeSearchParamValue = (searchParams: URLSearchParams) => {
  const queryValue = searchParams.get(MEDICAL_RECORD_FILTERS.REPORT_TYPE);
  return queryValue && queryValue.length > 0 ? queryValue?.split(',') : [];
};

export const getTagsSearchParamValue = (searchParams: URLSearchParams) => {
  const queryValue = searchParams.get(MEDICAL_RECORD_FILTERS.TAGS);
  return queryValue && queryValue.length > 0 ? queryValue?.split(',') : [];
};

export const getSortByParamValue = (searchParams: URLSearchParams) => {
  const queryValue = searchParams.get(MEDICAL_RECORD_FILTERS.SORT_BY);
  return queryValue && queryValue.length > 0 ? queryValue : '';
};

export const formatListToSelectOptions = (list: ValueSet[]) => {
  return list?.[0]?.compose?.include?.[0]?.concept?.map((pair) => ({
    value: pair?.code,
    label: pair?.display,
  }));
};

export const formatListToSelectOptionsExpand = (list: { code: string; display: string }[]) => {
  return list?.map((pair) => ({
    value: pair?.code,
    label: pair?.display,
  }));
};

function getDateFilter(fromDate?: string | null, toDate?: string | null) {
  if (!fromDate) {
    return undefined;
  }

  const fromDateStart = dayjs(fromDate).startOf('day').toISOString();
  const fromDateEnd = dayjs(fromDate).endOf('day').toISOString();

  if (fromDate && toDate && fromDate !== toDate) {
    return `date ge ${fromDate} and date le ${toDate}`;
  }

  return `date ge ${fromDateStart} and date le ${fromDateEnd}`;
}

export function getModifiedParams(searchParams: URLSearchParams) {
  const filterValues = Object.keys(CODE_SYSTEMS_MAPPING);

  const modifiedSearchParams = new URLSearchParams();

  searchParams.forEach((value, key) => {
    if (filterValues.includes(key)) {
      const modifiedValue = value
        ?.split(',')
        .map((item) => `${CODE_SYSTEMS_MAPPING[key]}|${item}`)
        .join(',');

      modifiedSearchParams.append(key, modifiedValue);
    }
  });

  const reportType = modifiedSearchParams.get('report_type');
  const tags = modifiedSearchParams.get('tags');
  const starred = modifiedSearchParams.get('starred');
  const sortBy = modifiedSearchParams.get('sort_by');

  const fromDate = searchParams.get('from_date');
  const toDate = searchParams.get('to_date');
  const description = searchParams.get('description');

  const keysWithEqualComparison = [reportType, tags, starred].filter(Boolean);
  const equalKeys = keysWithEqualComparison.map((key) => `_tag eq ${key}`).join(' and ');

  const newDesc = description ? `description co ${description}` : undefined;

  const combinedParams = {
    sort_by: sortBy ?? undefined,
    filter: [newDesc, getDateFilter(fromDate, toDate), equalKeys].filter(Boolean).join(' and ') || undefined,
    reminderUrl: COMM_REMINDER_EXTENSION,
  };

  return combinedParams;
}

export const validateForTrimmingStr = (value: string) => {
  const trimmedValue = value.trim();
  return trimmedValue.length > 0;
};

export function getPercentFromLabReport(result: number, low: number, high: number) {
  let percentInValue = 0;
  if (low && high && result) {
    percentInValue = ((result - low) / (high - low)) * 100;
    return Math.round(percentInValue * 100) / 100;
  }
  if (low && result) {
    return low <= result ? 50 : -Infinity;
  }
  if (high && result) {
    return result >= high ? Infinity : 50;
  }
  return undefined;
}
interface focus {
  reference: string;
}

type SecurityLabel = {
  coding: {
    system: string;
    code: string;
    display: string;
  }[];
}[];

export const createTaskPayload = (
  statusCode: string,
  taskStatus: string,
  patientId?: string | undefined,
  focus?: focus | undefined,
  organizationId?: string | undefined
): Task | null => {
  return {
    resourceType: 'Task',
    identifier: [
      {
        value: 'urn:fh-workflow:task:document-engine',
      },
    ],
    status: taskStatus,
    intent: 'order',
    businessStatus: {
      coding: [
        {
          code: statusCode,
        },
      ],
    },
    focus,
    for: {
      reference: patientId ? `Patient/${patientId}` : patientId,
    },
    requester: {
      reference: patientId ? `Patient/${patientId}` : patientId,
    },
    owner: {
      reference: organizationId,
    },
  };
};

interface ExtendedTaskMeta {
  lastUpdated?: string;
  accounts?: any; // Change 'any' to the correct data structure
  account?: any; // Change 'any' to the correct data structure
}

export interface ExtendedTask extends Task {
  meta?: ExtendedTaskMeta;
  output?: {
    type: {
      coding: {
        code: string;
        display: string;
      }[];
    };
  }[];
}

export const updateTaskPayload = (
  status: string,
  statusCode: string,
  task?: ExtendedTask,
  password?: string,
  patientId?: string | undefined
): ExtendedTask | null => {
  if (!task) return null; // Handle cases where task might be undefined
  return {
    resourceType: 'Task',
    identifier: task?.identifier?.map((e) => ({ value: e.value })),
    status: status || task?.status,
    intent: task?.intent,
    meta: {
      lastUpdated: task?.meta?.lastUpdated,
      accounts: task?.meta?.accounts || [
        {
          reference: patientId ? `Patient/${patientId}` : patientId,
        },
      ],
      account: {
        reference: patientId ? `Patient/${patientId}` : patientId,
      },
    },
    businessStatus: {
      coding: [
        {
          code: statusCode,
        },
      ],
    },
    input: [
      {
        valueString: password,
        type: {
          text: 'user pdf password',
        },
      },
    ],
    focus: {
      reference: task?.focus?.reference,
    },
    for: {
      reference: task?.for?.reference,
    },
    requester: {
      reference: task?.requester?.reference,
    },
    owner: {
      reference: task?.owner?.reference,
    },
    id: task?.id,
  };
};

export const createDocumentReferencePayload = (
  url: string,
  DocumentReference?: DocumentReferenceType,
  patientId?: string | undefined,
  securityLabel?: SecurityLabel | undefined
): DocumentReferenceType | null => {
  if (!DocumentReference) return null; // Handle cases where DocumentReference might be undefined
  return {
    resourceType: 'DocumentReference',
    status: 'current',
    docStatus: 'preliminary',
    // type, // This is not mapped yet
    subject: {
      reference: patientId ? `Patient/${patientId}` : patientId,
    },
    securityLabel,
    // [
    //   {
    //     coding: [
    //       {
    //         system: 'http://terminology.hl7.org/CodeSystem/v3-ObservationValue',
    //         code: 'MASKED',
    //         display: 'masked',
    //       },
    //     ],
    //   },
    // ]
    content: [
      {
        attachment: {
          contentType: 'image/jpg',
          url,
          // data, size, hash, title, creation
        },
      },
    ],
    meta: {
      tag: [
        {
          system: 'https://fluentinhealth.com/FHIR/CodeSystem/FluentHealthUI',
          code: 'password-protected:false',
          display: 'clear text',
        },
        {
          system: 'https://fluentinhealth.com/FHIR/CodeSystem/FluentHealthUI',
          code: 'source:upload',
          display: 'upload',
        },
      ],
    },
  };
};

export const isTaskRejectedForReason = (task?: Task[], rejectionCode?: string): boolean => {
  return (
    task?.some(({ output }) => output?.some(({ type }) => type?.coding?.some(({ code }) => code === rejectionCode))) ||
    false
  );
};

const getTagArray = (tag: string | null) => tag?.split(',').map((t) => t.trim()) || [];

const isDateInRange = (contextStart: string | undefined, fromDate: string | null, toDate: string | null) => {
  if (fromDate && toDate) {
    if (!contextStart) return false;
    const start = new Date(contextStart);
    return start >= new Date(fromDate) && start <= new Date(toDate);
  }
  return true;
};

const hasMatchingTag = (recordTags: any[], tagArray: string[]) => {
  if (!tagArray.length) return true;
  return recordTags?.some((tag) => tagArray.includes(tag.code));
};

const isBookmarked = (recordTags: any[], isActive: boolean) => {
  if (!isActive) return true;
  return recordTags.some(
    (tag) => tag.code === 'mr-book' && tag.system === 'https://fluentinhealth.com/FHIR/CodeSystem/FluentHealthUI'
  );
};
const extractSearchParams = (searchParams: any) => ({
  isBookmarkFilterActive: searchParams.get(MEDICAL_RECORD_FILTERS.STARRED) === 'mr-book',
  fromDate: searchParams.get(MEDICAL_RECORD_FILTERS.FROM_DATE),
  toDate: searchParams.get(MEDICAL_RECORD_FILTERS.TO_DATE),
  reportType: searchParams.get(MEDICAL_RECORD_FILTERS.REPORT_TYPE),
  tagArray: getTagArray(searchParams.get(MEDICAL_RECORD_FILTERS.TAGS)),
});

const filterMedicalRecords = (
  docIds: string[],
  medicalRecordList: any[],
  searchParams: any,
  mapper: (record: any) => {
    focusRef: string | undefined;
    contextPeriodStart: string | undefined;
    tags: any[];
    docTypeCode: string | undefined;
  }
) => {
  const { isBookmarkFilterActive, fromDate, toDate, reportType, tagArray } = extractSearchParams(searchParams);

  return (
    medicalRecordList?.filter((record: any) => {
      const { focusRef, contextPeriodStart, tags, docTypeCode } = mapper(record);

      const isLinkedToDoc =
        docIds.length > 0
          ? docIds.some((docId) => focusRef === docId || focusRef === `DocumentReference/${docId}`)
          : true;

      return (
        isLinkedToDoc &&
        isBookmarked(tags, isBookmarkFilterActive) &&
        isDateInRange(contextPeriodStart, fromDate, toDate) &&
        (reportType ? reportType === docTypeCode : true) &&
        hasMatchingTag(tags, tagArray)
      );
    }) || []
  );
};

// Reusable exports
export const fetchMedicalRecordListByDocIds = (docIds: string[], medicalRecordList: any[], searchParams: any) =>
  filterMedicalRecords(docIds, medicalRecordList, searchParams, (record) => {
    const focusResource = record?.focus?.resource;
    return {
      focusRef: record?.focus?.reference,
      contextPeriodStart: focusResource?.context?.period?.start,
      tags: focusResource?.meta?.tag || [],
      docTypeCode: focusResource?.type?.coding?.[0]?.code,
    };
  });

export const fetchMedicalRecordListActionNeededByDocIds = (
  docIds: string[],
  medicalRecordList: any[],
  searchParams: any
) =>
  filterMedicalRecords(docIds, medicalRecordList, searchParams, (record) => {
    const focusResource = record?.docref;
    return {
      focusRef: focusResource?.id,
      contextPeriodStart: focusResource?.context?.period?.start,
      tags: focusResource?.meta?.tag || [],
      docTypeCode: focusResource?.type?.coding?.[0]?.code,
    };
  });

export async function sha1(input: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(input);
  const hashBuffer = await crypto.subtle.digest('SHA-1', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map((b) => b.toString(16).padStart(2, '0')).join('');
  return hashHex;
}

export const checkDocumentDuplicate = (hash: string, medicalRecordList: any): boolean => {
  if (!hash || !Array.isArray(medicalRecordList)) {
    return false;
  }

  return medicalRecordList.some((record: any) =>
    record?.focus?.resource?.content?.some((item: any) => {
      return item?.attachment?.hash === hash;
    })
  );
};
