import { useInfiniteQuery, useIsFetching } from '@tanstack/react-query';
import { useEffect, useRef, useState } from 'react';
import { useDebounce } from 'usehooks-ts';
import { Client, useClient } from 'urql';
import axios from 'axios';
import { enumStatus } from '@user/lib/constants';
import { getSearchSuggestion } from '@services/elasticSearch';

import { filterTasksByCode, useIntersected } from '@lib/utils/utils';
import { Patient } from '@lib/models/patient';
import {
  DocumentReference,
  // Attachment,
  // DocumentReference,
  // Location,
  Patient as MedplumPatient,
  // MyRecordsQuery,
  // QuestionnaireResponse,
} from 'src/gql/graphql';
import { medplumApi } from './medplum-api';
import {
  EDIT_RECORD_VALUESET_URLS,
  documentPasswordSubmission,
  identifierUrn,
  // QUESTIONNAIRE_URL
} from './constants';
import { ExtendedTask, fetchMedicalRecordListByDocIds, formatListToSelectOptions, getModifiedParams } from './utils';
import {
  getDocData,
  getDocumentReference,
  getFileFromTask,
  getListOfTags,
  getObservationList,
} from './medplum-graphql-query';
import { MEDPLUM_GRAPHQL_API_URL } from '@lib/constants';
import { AuthService } from '@lib/authService';
import { usePublicRecordSettings } from '@lib/state';
import {
  FH_CODE_SYSTEM_USER_META_TAG,
  FH_STRUCTURE_DEFINITION_DOCUMENTREFERENCE_NOTE,
  FH_STRUCTURE_DEFINITION_PRESCRIBED,
} from 'src/constants/medplumConstants';
import {
  MEDICAL_RECORD_BEHOLDER_TYPES,
  MEDICAL_RECORD_FILTERS,
  MEDICAL_RECORD_TYPES,
} from '@lib/models/medical-record';

export const STATE_TYPES = {
  MEDICAL_RECORD: 'medical_record',
  MEDICAL_RECORDS_ACTION_NEEDED_LIST: 'medical_records_action_needed_list',
  MEDICAL_RECORDS_REVIEW_LIST: 'medical_records_review_list',
  MEDICAL_RECORDS_LIST: 'medical_records_list',
  MEDICAL_RECORDS_ALL_LIST: 'medical_records_all_list',
  MEDICAL_RECORD_TAG_LIST: 'medical_record_tag_list',
  MEDICAL_RECORD_TYPE_LIST: 'medical_record_type_list',
  MEDICAL_RECORD_SUGGESTION_LIST: 'medical_record_suggestion_list',
};
export enum enumContentType {
  DICOM = 'application/dicom',
  PLAINTEXT = 'text/plain',
  JSON = 'application/json',
}
export interface MedicalRecord {
  id: string;
  title: string;
  status: enumStatus.PROCESSING_PAUSED | enumStatus.REJECTED;
  encrypted?: boolean;
  type: 'others' | 'email';
  message: string;
  task?: ExtendedTask;
  doc: DocumentReference;
}

export const sharedMedicalRecordActions = () => {
  const mutateDeleteRecord = async (recordId: string, taskId: string) =>
    medplumApi.medicalRecord.deleteOne(recordId, taskId);

  return {
    deleteMedicalRecord: mutateDeleteRecord,
  };
};

export const useIsLoadingMedicalRecords = (patientId: Patient['id'], filters: Record<string, string | number> = {}) => {
  return !!useIsFetching([STATE_TYPES.MEDICAL_RECORDS_LIST, patientId, filters]);
};

export async function getDocRef(variables: any) {
  // const response = await (variables);
  const {
    data: { data },
    // status,
  } = await axios.post(
    MEDPLUM_GRAPHQL_API_URL,
    {
      query: getDocData.getTaskQuery({
        identifier: identifierUrn,
        ...variables,
      }),
    },
    {
      headers: AuthService.instance.withAuthHeader(),
    }
  );

  return data?.TaskList || [];
}
export async function getAllDocumentRefer(variables: any) {
  const {
    data: { data },
  } = await axios.post(
    MEDPLUM_GRAPHQL_API_URL,
    {
      query: getDocData.getTaskQuery({
        identifier: identifierUrn,
        ...variables,
      }),
    },
    {
      headers: AuthService.instance.withAuthHeader(),
    }
  );
  return data?.TaskList || [];
}
export const getAllDocumentReference = (patientId?: MedplumPatient['id'], suspense: boolean = true) => {
  const queryKey = [STATE_TYPES.MEDICAL_RECORDS_ALL_LIST, patientId];

  const getAll = () => {
    return getAllDocumentRefer({
      patientId: `Patient/${patientId}`,
    });
  };

  const { data } = useInfiniteQuery(queryKey, getAll, {
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
    enabled: !!patientId,
    suspense,
  });
  const medicalRecordList = data?.pages?.[0] ?? [];

  return {
    medicalRecordList,
  };
};

export async function getObservations(client: Client, variables: any) {
  const response = await client.query(getObservationList, { id: variables });
  return response?.data?.Observation ?? {};
}

export async function downloadRecord(client: Client, variables: any) {
  const response = await client.query(getFileFromTask, { id: variables });
  return response?.data?.Task ?? {};
}
export async function downloadFileQuery(client: Client, variables: string) {
  const response = await client.query(getDocumentReference, { id: variables });
  return response?.data?.DocumentReference ?? {};
}

export const useMedicalRecordList = (
  patientId?: MedplumPatient['id'],
  filters = {},
  businessStatus?: string,
  suspense: boolean = true
) => {
  // const client = useClient();

  const queryKey = [STATE_TYPES.MEDICAL_RECORDS_LIST, patientId];

  const getAll = () => {
    return getDocRef(
      {
        ...(filters ?? {}),
        patientId: `Patient/${patientId}`,
        business_status: businessStatus,
      }
      // {
      // ...filters,
      // page: pageParam,}
    );
  };

  const { data, fetchNextPage, hasNextPage, isLoading, isFetching, isFetchingNextPage, refetch } = useInfiniteQuery(
    queryKey,
    getAll,
    {
      // getNextPageParam: (lastPage) =>
      //   lastPage?.current_page < lastPage?.last_page ? (lastPage.current_page ?? 0) + 1 : undefined,
      notifyOnChangeProps: ['data', 'error'],
      refetchOnWindowFocus: false,
      // staleTime: 1000,
      enabled: !!patientId,
      suspense,
    }
  );

  const { deleteMedicalRecord } = sharedMedicalRecordActions();

  const medicalRecordList = data?.pages?.[0] ?? [];

  return {
    fetchMedicalRecordList: refetch,
    medicalRecordList,
    deleteMedicalRecord,
    // lazy loading params
    fetchNextPage,
    hasNextPage,
    isLoading,
    isFetching,
    isFetchingNextPage,
  };
};
export const funLocalMedicalRecord = (
  medicalRecordList: any[] = [],
  setLocalMedicalRecordList: (data: any[]) => void,
  setLocalMedicalRecordListActionNeeded: (data: any[]) => void,
  setLocalMedicalRecordListReview: (data: any[]) => void
) => {
  // Early return for empty lists
  if (!medicalRecordList?.length) return;

  // Memoize common transform function to avoid recreation
  const createDocrefTransform = (actionStatus: string) => (task: any) => ({
    actionStatus,
    docref: { ...task?.focus?.resource, task },
  });

  // Process all task types in parallel for better performance
  const [completedTasks, rejectedTasks, reviewPausedTasks, reviewInProgressTasks] = [
    // Completed tasks
    filterTasksByCode({
      tasks: medicalRecordList,
      status: 'completed',
    }),

    // Rejected tasks with transform
    filterTasksByCode({
      tasks: medicalRecordList,
      status: 'rejected',
      transform: createDocrefTransform('Rejected'),
    }),

    // Review paused tasks (password submission required)
    filterTasksByCode({
      tasks: medicalRecordList,
      codeSet: documentPasswordSubmission,
      mode: 'include',
      transform: createDocrefTransform(enumStatus.PROCESSING_PAUSED),
    }),

    // Review in-progress tasks (excluding password submission)
    filterTasksByCode({
      tasks: medicalRecordList,
      codeSet: documentPasswordSubmission,
      mode: 'exclude',
      status: 'in-progress',
    }),
  ];

  // Batch state updates to avoid multiple re-renders
  setLocalMedicalRecordList(completedTasks);
  setLocalMedicalRecordListActionNeeded([...rejectedTasks, ...reviewPausedTasks]);
  setLocalMedicalRecordListReview(reviewInProgressTasks);
};
export const funLocalShareMedicalRecord = (medicalRecordList: any, setLocalMedicalRecordList: any) => {
  if (medicalRecordList.length > 0) {
    setLocalMedicalRecordList(medicalRecordList);
  }
};
export const useMedicalRecordListLazyPagination = (
  patientId: MedplumPatient['id'],
  filters: NonNullable<ReturnType<typeof getModifiedParams>>,
  shareStateIdentifier: string,
  businessStatus: string
) => {
  const loadingElementRef = useRef(null);
  const pubSettings: any = usePublicRecordSettings();
  const [localMedicalRecordList, setLocalMedicalRecordList] = useState<any[]>([]);
  const [localMedicalRecordListActionNeeded, setLocalMedicalRecordListActionNeeded] = useState<any[]>([]);
  const [localMedicalRecordListReview, setLocalMedicalRecordListReview] = useState<any[]>([]);

  // Handle public mode scenario
  if (pubSettings.isPublicRecordMode && shareStateIdentifier && shareStateIdentifier in pubSettings) {
    useEffect(
      () => funLocalShareMedicalRecord(pubSettings[shareStateIdentifier], setLocalMedicalRecordList),
      [pubSettings[shareStateIdentifier]]
    );
    const updateLocalMedicalRecordList = (docIds: any[]) => {
      const filteredRecords = pubSettings[shareStateIdentifier].filter((record: any) => {
        const isLinkedToDoc = docIds.length > 0 ? docIds.some((docId) => record?.resource?.id === docId) : true;
        return isLinkedToDoc;
      });
      setLocalMedicalRecordList(filteredRecords);
    };
    return {
      medicalRecordList: localMedicalRecordList,
      medicalRecordListActionNeeded: [],
      medicalRecordListReview: [],
      updateLocalMedicalRecordList,
      loadingElementRef,
      fetchMedicalRecordList: () => {},
      shouldLoadMore: false,
      hasNextPage: false,
      isLoading: false,
    };
  }
  const { isIntersection: shouldLoadMore } = useIntersected(loadingElementRef);
  const debouncedShouldLoadMore = useDebounce(shouldLoadMore, 500);

  const { medicalRecordList, fetchNextPage, hasNextPage, isFetching, isLoading, fetchMedicalRecordList } =
    useMedicalRecordList(patientId, filters, businessStatus);

  useEffect(() => {
    if (shouldLoadMore && hasNextPage && !isFetching) {
      fetchNextPage();
    }
  }, [shouldLoadMore, hasNextPage, isFetching]);
  useEffect(() => {
    funLocalMedicalRecord(
      medicalRecordList,
      setLocalMedicalRecordList,
      setLocalMedicalRecordListActionNeeded,
      setLocalMedicalRecordListReview
    );
  }, [medicalRecordList]);
  const updateLocalMedicalRecordList = (docIds: any[], searchParams: any) => {
    // Early return for empty lists
    if (!medicalRecordList?.length) return;

    // Reuse the same transform function pattern for consistency
    const createDocrefTransform = (actionStatus: string) => (task: any) => ({
      actionStatus,
      docref: { ...task?.focus?.resource, task },
    });

    // Process all task types efficiently
    const [completedTasks, rejectedTasks, reviewPausedTasks, reviewInProgressTasks] = [
      filterTasksByCode({
        tasks: medicalRecordList,
        status: 'completed',
      }),

      filterTasksByCode({
        tasks: medicalRecordList,
        status: 'rejected',
        transform: createDocrefTransform('Rejected'),
      }),

      filterTasksByCode({
        tasks: medicalRecordList,
        codeSet: documentPasswordSubmission,
        mode: 'include',
        transform: createDocrefTransform(enumStatus.PROCESSING_PAUSED),
      }),

      filterTasksByCode({
        tasks: medicalRecordList,
        codeSet: documentPasswordSubmission,
        mode: 'exclude',
        status: 'in-progress',
      }),
    ];

    // Process filtered records in parallel
    const [filteredRecords, filteredRecordsActionNeeded, filteredRecordsReview] = [
      fetchMedicalRecordListByDocIds(docIds, completedTasks, searchParams),
      fetchMedicalRecordListByDocIds(docIds, [...rejectedTasks, ...reviewPausedTasks], searchParams),
      fetchMedicalRecordListByDocIds(docIds, reviewInProgressTasks, searchParams),
    ];

    // Batch state updates
    setLocalMedicalRecordList(filteredRecords);
    setLocalMedicalRecordListActionNeeded(filteredRecordsActionNeeded);
    setLocalMedicalRecordListReview(filteredRecordsReview);
  };
  return {
    medicalRecordList: localMedicalRecordList,
    medicalRecordListActionNeeded: localMedicalRecordListActionNeeded,
    medicalRecordListReview: localMedicalRecordListReview,
    updateLocalMedicalRecordList,
    loadingElementRef,
    fetchMedicalRecordList,
    shouldLoadMore: debouncedShouldLoadMore,
    hasNextPage,
    isLoading,
  };
};

export const useMedicalRecord = () => {
  const { deleteMedicalRecord } = sharedMedicalRecordActions();

  return {
    deleteMedicalRecord,
  };
};

// Todo can be removed safely after testing
export const useMedicalRecordTagList = (patientId: Patient['id'], setMedicalRecordTagList: Function) => {
  const client = useClient();

  useEffect(() => {
    async function getListOfSelectedTags() {
      const listOfTags = await client.query(getListOfTags, { patientId, code: 'UserMetaTag' });
      setMedicalRecordTagList(listOfTags?.data?.ListList?.[0]?.entry);
    }

    getListOfSelectedTags();
  }, []);
};

export const useMedicalRecordSuggestionList = (
  patientId: Patient['id'],
  filters: string,
  bookmark: string,
  fromDate: string,
  toDate: string,
  reportType: string,
  tag: string
) => {
  // const client = useClient();
  const queryKey = [STATE_TYPES.MEDICAL_RECORD_SUGGESTION_LIST, patientId, filters];

  const getAll = () => {
    return getSearchSuggestion({ filters, bookmark, fromDate, toDate, reportType, tag });
  };

  const { data, fetchNextPage, hasNextPage, isLoading, isFetching, isFetchingNextPage } = useInfiniteQuery(
    queryKey,
    getAll,
    {
      // getNextPageParam,
      notifyOnChangeProps: ['data', 'error'],
      refetchOnWindowFocus: false,
      // staleTime: 1000,
      enabled: !!patientId,
      suspense: false,
    }
  );
  const extractDescriptionsOrTitles = (details: any): string[] => {
    return details?.pages?.flatMap((page: any) =>
      page?.hits?.hits?.map((hit: any) => {
        let description = hit?._source.description;
        (hit._source?.content || []).some((item: any) => {
          const contentType = item?.attachment?.contentType;

          if (
            !description &&
            contentType !== enumContentType.DICOM &&
            contentType !== enumContentType.JSON &&
            contentType !== enumContentType.PLAINTEXT
          ) {
            description = item?.attachment?.title || null;
          }

          return description; // Stops iteration when both values are set
        });
        return { ...hit?._source, description };
      })
    );
  };

  // Concatenate all data pages into a single array
  const suggestionList = extractDescriptionsOrTitles(data);
  return {
    suggestionList,
    // lazy loading params
    fetchNextPage,
    hasNextPage,
    isLoading,
    isFetching,
    isFetchingNextPage,
  };
};

// TODO: Will need to change this in Eastic search task
export const useMedicalRecordSuggestionListLazyPagination = (
  patientId: Patient['id'],
  filters: string,
  myDocumentReferenceList: any[] | undefined,
  debouncedSearchText: string,
  searchParams: any
) => {
  const loadingElementRef = useRef(null);
  const bookmark = searchParams.get(MEDICAL_RECORD_FILTERS.STARRED);
  const fromDate = searchParams.get(MEDICAL_RECORD_FILTERS.FROM_DATE);
  const toDate = searchParams.get(MEDICAL_RECORD_FILTERS.TO_DATE);
  const reportType = searchParams.get(MEDICAL_RECORD_FILTERS.REPORT_TYPE);
  const tag = searchParams.get(MEDICAL_RECORD_FILTERS.TAGS);

  const { isPublicRecordMode } = usePublicRecordSettings();
  if (isPublicRecordMode) {
    const getDocumentTitle = (docRef: any): string | null => {
      let title: string | null = null;

      (docRef?.content || []).some((item: any) => {
        const contentType = item?.attachment?.contentType;

        if (
          !title &&
          contentType !== enumContentType.DICOM &&
          contentType !== enumContentType.JSON &&
          contentType !== enumContentType.PLAINTEXT
        ) {
          title = item?.attachment?.title || null;
        }

        return !!title;
      });

      return docRef?.description ?? title;
    };

    return {
      suggestionList: myDocumentReferenceList
        ?.map((val) => {
          const docRef = val?.resource;
          return { ...docRef, description: getDocumentTitle(docRef) };
        })
        ?.filter((docRef) => docRef?.description?.toLowerCase().includes(debouncedSearchText?.toLowerCase())),
      loadingElementRef,
      shouldLoadMore: false,
      hasNextPage: false,
      isLoading: false,
      isFetching: false,
    };
  }

  const { isIntersection: shouldLoadMore } = useIntersected(loadingElementRef);
  const debouncedShouldLoadMore = useDebounce(shouldLoadMore, 500);

  const { suggestionList, fetchNextPage, hasNextPage, isFetching, isLoading } = useMedicalRecordSuggestionList(
    patientId,
    filters,
    bookmark,
    fromDate,
    toDate,
    reportType,
    tag
  );

  useEffect(() => {
    if (shouldLoadMore && hasNextPage && !isFetching) {
      fetchNextPage();
    }
  }, [shouldLoadMore, hasNextPage, isFetching]);

  return {
    suggestionList,
    loadingElementRef,
    shouldLoadMore: debouncedShouldLoadMore,
    hasNextPage,
    isLoading,
    isFetching,
  };
};

type UseDocRefDetails = {
  docRef: any;
  taskId?: string;
};

// Function to safely extract required data
const extractData = (docRef: any) => {
  if (!docRef?.context?.related) return {}; // Return empty object if no related data

  const { related = [], encounter = [] } = docRef.context;

  // Extract DiagnosticReport
  const diagnosticReport =
    related.find((item: any) => item.resource?.__typename === 'DiagnosticReport')?.resource ?? null;

  // Extract Practitioner name
  const practitioner = related.find((item: any) => item.resource?.__typename === 'Practitioner')?.resource;
  const practitionerName = practitioner?.name?.[0]?.text || null;

  // Extract Family Member Name
  const familyMemberHistory = related.find(
    (item: any) => item.resource?.__typename === 'FamilyMemberHistory'
  )?.resource;
  const familyMemberName = familyMemberHistory?.familyMemberName || null;
  // Extract PractitionerRole specialty
  const practitionerRole = related.find((item: any) => item.resource?.__typename === 'PractitionerRole')?.resource;
  const specialty = practitionerRole?.specialty?.[0].coding?.[0];

  // Extract Condition conditionCode
  const condition = related.find((item: any) => item.resource?.__typename === 'Condition')?.resource;
  const conditionCodes = condition?.conditionCode?.coding?.[0];
  const medicationStatement = related?.find(
    (item: any) => item.resource?.__typename === 'MedicationStatement'
  )?.resource;
  const wasReferred = medicationStatement?.extension?.find(
    (item: any) => item?.url === FH_STRUCTURE_DEFINITION_PRESCRIBED
  )?.valueBoolean;

  // Extract Organization hospitalName
  const organization = related.find((item: any) => item.resource?.__typename === 'Organization')?.resource;
  const communicationRequest = related.find(
    (item: any) => item.resource?.__typename === 'CommunicationRequest'
  )?.resource;
  const hospitalName = organization?.hospitalName ?? null;
  const encounterDetails = encounter?.[0]?.resource?.class;
  return {
    diagnosticReport,
    practitioner,
    practitionerName,
    practitionerRole,
    specialty,
    condition,
    conditionCodes,
    communicationRequest,
    medicationStatement,
    wasReferred,
    organization,
    hospitalName,
    encounterDetails,
    familyMemberHistory,
    familyMemberName,
  };
};

// TODO - Update this to new mapping
export const useDocRefDetails = ({ docRef, taskId }: UseDocRefDetails) => {
  const isBookmarked = docRef?.meta?.tag?.some((obj: { code: string }) => obj?.code === 'mr-book');
  const isLabReport = docRef?.type?.coding?.some(
    (obj: { code: string }) => obj?.code === MEDICAL_RECORD_TYPES.LAB_RESULTS
  );
  const {
    diagnosticReport,
    practitioner,
    practitionerName,
    practitionerRole,
    specialty,
    condition,
    conditionCodes,
    communicationRequest,
    medicationStatement,
    wasReferred,
    organization,
    hospitalName,
    encounterDetails,
    familyMemberHistory,
    familyMemberName,
  } = extractData(docRef);

  const recordAttachments = docRef?.content?.map((attachmentObj: { attachment: any }) => ({
    ...(attachmentObj?.attachment ?? {}),
  }));
  const recordContentAttachments = docRef?.content;
  const docRefCreationDate = docRef?.context?.period?.start;
  const creationDate = docRef?.content?.[0]?.attachment?.creation;

  const notes = docRef?.extension?.find(
    (obj: any) => obj?.url === FH_STRUCTURE_DEFINITION_DOCUMENTREFERENCE_NOTE
  ).valueString;

  const tags = docRef?.meta?.tag?.length
    ? docRef?.meta?.tag?.filter((code: any) => code.system === FH_CODE_SYSTEM_USER_META_TAG)
    : [];
  const alert = docRef?.context?.related?.find(
    (res: { resource: { __typename: string } }) => res?.resource?.__typename === 'CommunicationRequest'
  )?.resource;

  let title: string | null = null;
  let fileName: string | null = null;
  let supportingURL: string | null = null;

  (docRef?.content || []).some((item: any) => {
    const contentType = item?.attachment?.contentType;

    if (
      !title &&
      contentType !== enumContentType.DICOM &&
      contentType !== enumContentType.JSON &&
      contentType !== enumContentType.PLAINTEXT
    ) {
      title = item?.attachment?.title || null;
      fileName = item?.attachment?.title || null;
    }
    if (!supportingURL && contentType === enumContentType.DICOM) {
      supportingURL = item?.attachment?.url || null;
    }

    return title && supportingURL; // Stops iteration when both values are set
  });

  title = docRef?.description ?? title;
  const medicalRecordType = docRef?.type?.coding?.[0] || {};

  const dateOnRecord = docRef?.context?.period?.start;

  const nameOnRecord =
    familyMemberName ||
    (docRef?.subject?.resource?.name?.[0]
      ? `${docRef.subject.resource.name[0].given?.join(' ') || ''} ${
          docRef.subject.resource.name[0].family || ''
        }`.trim()
      : null);

  return {
    id: docRef?.id,
    taskId,
    tags,
    alert,
    notes,
    title,
    supportingURL,
    recordContentAttachments,
    typeOfVisit: encounterDetails,
    fileName,
    medicalRecordFor: familyMemberName ? MEDICAL_RECORD_BEHOLDER_TYPES.OTHERS : MEDICAL_RECORD_BEHOLDER_TYPES.OWN,
    communicationRequest,
    nameOnRecord,
    dateOnRecord,
    location: hospitalName,
    medicalRecordType,
    doctorName: practitionerName,
    // To-Do: change to real mapping
    wasReferred,
    diagnosticReport,
    isBookmarked,
    conditionType: conditionCodes,
    speciality: specialty,
    recordAttachments,
    docRefCreationDate,
    isLabReport,
    practitioner,
    practitionerRole,
    condition,
    medicationStatement,
    organization,
    creationDate,
    familyMemberHistory,
    familyMemberName,
  };
};

// Extracts document reference data from various input formats (Task, RelatedArtifact, etc.)
export const useExtractDocumentResource = (input: any) => {
  // Normalize and extract key fields from a DocumentReference-like resource
  const extractResource = (extract: any) => {
    let resource = extract?.resource || extract?.valueReference?.resource || extract;

    // Use nested focus.resource if present (e.g., from Task or wrapper object)
    if (resource?.focus?.resource) {
      resource = resource.focus.resource;
    }

    return {
      // Get document ID from resource or fallback from reference string
      id: resource?.id || (resource?.reference ? resource.reference.split('/').pop() : null),

      // Prefer description; fallback to first attachment title
      title: resource?.description || resource?.content?.[0]?.attachment?.title || null,

      // Extract document type code if available
      type: resource?.type?.coding?.[0]?.code || null,

      // Prefer attachment creation date, else use resource date or last updated
      date: resource?.content?.[1]?.attachment?.creation || resource?.date || resource?.meta?.lastUpdated || null,

      // Try getting related document/task reference ID
      docRefId:
        input?.[0]?.focus?.reference ||
        resource?.reference ||
        resource?.context?.related?.[0]?.resource?.id ||
        (resource?.id ? `DocumentReference/${resource?.id}` : null) ||
        null,

      // Get all attachments from content array
      attachments: resource?.content?.map((c: any) => c.attachment) || null,
    };
  };

  // Normalize single object input to array
  if (!Array.isArray(input)) return [extractResource(input)];

  // Support Task.detail[], valueReference, or raw DocumentReference[]
  return input.flatMap((e) =>
    e.detail
      ? e.detail.map(extractResource)
      : e.valueReference
      ? [extractResource(e.valueReference)]
      : [extractResource(e)]
  );
};

export const useMedicalRecordDisplayCategories = ({ setCategories }: { setCategories: Function }) => {
  const [isCategoryLoading, setIsCategoryLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsCategoryLoading(true);
        const result =
          (await medplumApi?.valueSets?.getAll({
            urls: EDIT_RECORD_VALUESET_URLS?.filter((item) => item?.type === 'medicalRecordCategories'),
          })) || {};

        setCategories(formatListToSelectOptions(result?.medicalRecordCategories || []));
      } catch (error) {
        console.error('error ::', error);
      } finally {
        setIsCategoryLoading(false);
      }
    };

    fetchData();
  }, []);

  return {
    isCategoryLoading,
  };
};

export const getOrganizationList = async () => {
  const {
    data: { data },
  } = await axios.post(
    MEDPLUM_GRAPHQL_API_URL,
    {
      query: getDocData.getOrganization(),
    },
    {
      headers: AuthService.instance.withAuthHeader(),
    }
  );

  return data?.OrganizationList?.[0]?.id;
};
