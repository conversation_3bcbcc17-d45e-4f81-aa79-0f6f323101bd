export enum enumPreventiveScreeningTab {
  ANNUAL_PHYSICAL = 'Full-Body Check-Up',
  EYE_EXAMINATION = 'Eye Examination',
  DENTAL_CHECKUP_AND_CLEANING = 'Dental Check-up and Cleaning',
  SKIN_SELF_EXAMINATION = 'Skin Self-examination',
  BREAST_SELF_EXAMINATION = 'Breast Self-evaluation',
  TESTICULAR_SELF_EXAMINATION = 'Testicular Self-examination',
  COMPLETE_BLOOD_COUNT = 'Complete Blood Count (CBC)',
  THYROID_TEST = 'Thyroid Test',
  BLOOD_SUGAR_TEST = 'Blood Sugar Test',
  LIPID_PROFILE = 'Lipid Profile',
  ALLERGY_TEST = 'Allergy Test',
  PROSTATE_SPECIFIC_ANTIGEN = 'Prostate-specific Antigen (PSA)',
  PAP_SMEAR = 'Pap Smear',
  MAMMOGRAM = 'Mammogram',
  ELECTROCARDIOGRAM = 'Electrocardiogram (ECG or EKG)',
  TWO_D_ECHOCARDIOGRAM = '2D Echocardiogram',
  TREADMILL_TEST = 'Treadmill Test (TMT)',
  COLONOSCOPY = 'Colonoscopy',
  SEXUALLY_TRANSMITTED_INFECTIONS = 'Sexually Transmitted Infections (STI)',
}

export const preventiveScreeningTab = Object.entries(enumPreventiveScreeningTab).map(([key, text]) => ({
  text,
  id: key,
}));
