import branch from '@services/branch';

interface GenerateBranchUrlOptions {
  path: string;
  data?: Record<string, unknown>;
}

// TODO: The creation of Branch.io URLs should be from Secure AM path - https://help.branch.io/developers-hub/reference/createdeeplinkurl
export const generateBranchUrl = async ({ path, data }: GenerateBranchUrlOptions): Promise<string> => {
  try {
    const baseUrl = `${window.location.origin}`;
    const fullPath = path ? `${baseUrl}/${path}` : `${baseUrl}/${window.location.pathname}`;

    const branchData = {
      canonicalIdentifier: path,
      data: {
        $desktop_url: fullPath,
        ...data,
      },
    };

    const branchUrl = await new Promise<string>((resolve, reject) => {
      branch.link(branchData, (err: any, link: string | null) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(link ?? '');
      });
    });

    return branchUrl;
  } catch (error) {
    console.error('Error generating Branch.io URL:', error);
    throw error;
  }
};
