import React from 'react';
import ReactDOM from 'react-dom/client';
import { RouterProvider } from 'react-router-dom';
// import clevertap from 'clevertap-web-sdk';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { router } from './routes';
import './globals.css';
// import { CLEVERTAP_KEY } from '@lib/constants';

// NOTE: TODO: the commented code we required if in case of our updated code is not worked
if ('serviceWorker' in navigator && 'PushManager' in window) {
  navigator.serviceWorker
    .register('src/service-worker.js')
    .then(function (registration) {
      // console.log('Service Worker registered with scope:', registration.scope);

      // Now you can use the 'registration' object for push notifications, background sync, etc.
      // Example: sending push notification to service worker

      registration.showNotification('Service Worker Registered!', {
        body: 'Your service worker is active and running.',
        icon: '/assets/icons/fh-icon.png',
        badge: '/assets/icons/fh-logo.svg',
      });
    })
    .catch(function (error) {
      console.error('Service Worker registration failed:', error);
    });
}

// Request permission for push notifications
// if (Notification?.permission === 'default') {
//   Notification?.requestPermission().then(function (permission) {
//     if (permission === 'granted') {
//       console.log('Permission granted for push notifications');
//     } else {
//       console.log('Permission denied for push notifications');
//     }
//   });
// }
const { DASHBOARD } = ROUTE_VARIABLES;
const { VIEW } = ROUTE_ACTIONS;

if (window.location.pathname === '/') {
  window.location.pathname = `/${DASHBOARD}/${VIEW}`;
}

// clevertap.init(CLEVERTAP_KEY, 'in1');

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <React.StrictMode>
    <RouterProvider router={router} />
  </React.StrictMode>
);

// serviceWorker.register();
