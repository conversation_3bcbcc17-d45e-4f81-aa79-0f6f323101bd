import dayjs from 'dayjs';
import { Bell as <PERSON><PERSON><PERSON>, Clock, Edit3, Paperclip as PaperclipIcon, Trash } from 'react-feather';
import {
  Box,
  Button,
  Center,
  ChakraProps,
  Divider,
  Drawer,
  DrawerBody,
  DrawerContent,
  DrawerOverlay,
  Flex,
  Heading,
  IconButton,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Skeleton,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  UseDisclosureReturn,
  useDisclosure,
  useOutsideClick,
  useTheme,
} from '@chakra-ui/react';
import React, { LegacyRef, Suspense, forwardRef, useEffect, useRef, useState } from 'react';
import shallow from 'zustand/shallow';
import { Link } from 'react-router-dom';
// import clevertap from 'clevertap-web-sdk';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import {
  //  ALERT_FREQUENCY,
  Reminder,
  //  AlertSchedule
} from '@lib/models/alert';
import { useA<PERSON>tList, useAlertListLazyPagination, useAnalyticsService, useAuthService } from '@lib/state';
import {
  FH_UI_CODESYSTEM,
  MEDICAL_RECORD_ICON_BG_MAP,
  MEDICAL_RECORD_ICON_MAP,
  MEDICAL_RECORD_NAME_MAP,
} from '../app/medical-records/lib/constants';
import { FluentHealthLoader } from './FluentHealthLoader';
import { Modal } from './Modal';
import { MoreActionsMenu, MoreActionsMenuButton, MoreActionsMenuItem, MoreActionsMenuList } from './ui/Menu';
import { ReminderFlow } from 'src/app/medical-records/components/ReminderFlow';
import { useNotificationDisclosure } from '@lib/utils/utils';
import { MEDICAL_RECORD_TYPES } from '@lib/models/medical-record';
import {
  AnalyticsEventName,
  AnalyticsFlow,
  EventFlowActions,
  EventFlowNames,
  EventPropsNames,
} from '@lib/analyticsService';

import { ReactComponent as EmptyStateIcon } from '@assets/objects/notifications-empty-state.svg';
import { ReactComponent as CalendarIcon } from '@assets/icons/calendar.svg';
import { ReactComponent as CheckmarkIcon } from '@assets/icons/checkmark.svg';
import { ReactComponent as HamburgerMenu } from '@assets/objects/hamburger-menu.svg';
import { ReactComponent as FluentHealthLogo } from '@assets/icons/fh-logo.svg';

// const ONCE_IN_FREQUENCY_LABEL_MAP = {
//   Days: [ALERT_FREQUENCY.DAILY],
//   Weeks: [ALERT_FREQUENCY.WEEKLY],
//   Months: [ALERT_FREQUENCY.MONTHLY],
//   Years: [ALERT_FREQUENCY.YEARLY],
// };

const REMINDER_DATE_FORMAT = 'ddd, DD MMM, YYYY';
const REMINDER_TIME_FORMAT = 'HH:mm A';
const TWO_HOURS_BEFORE_EXPIRATION = 2;
// const CUSTOM_ALERT_FREQUENCY = 'rf-custom';
// const CUSTOM_BASE_ALERT_FREQUENCY = 'rf-customBase';
const getAlertTime = (alert: Reminder): { time: string; isExpired: boolean; isExpiresSoon: Boolean; date: string } => {
  // let nextAlertTime: string | undefined;
  const now = new Date();
  const tomorrow = new Date().setHours(now.getHours() + TWO_HOURS_BEFORE_EXPIRATION);

  // const nextSchedule: AlertSchedule | undefined = alert.schedules?.find((item) => {
  //   const isPending = Number(item.status) === 0;
  //   const notExpired = dayjs(alert?.occurrenceDateTime).isAfter(now);

  //   return notExpired && isPending;
  // });

  // if (nextSchedule) {
  //   nextAlertTime = `${nextSchedule.alert_date}T${nextSchedule.alert_time}`;
  // } else {
  // }

  // const notExpired = dayjs(alert?.occurrenceDateTime).isAfter(now);

  // if (alert.status === 'active') {
  //   nextAlertTime = alert?.occurrenceDateTime;
  // }

  const dateTime = dayjs(alert?.occurrenceDateTime ?? '');
  const isExpired = alert?.status === 'completed';
  const isExpiresSoon = dateTime.isAfter(tomorrow);

  return {
    isExpired,
    isExpiresSoon,
    date: dateTime.format(REMINDER_DATE_FORMAT),
    time: dateTime.format(REMINDER_TIME_FORMAT),
  };
};

function NotificationTab({ text, counter }: { text: string; counter?: number | null }) {
  return (
    <Tab
      flex="1"
      pb="12px"
      fontSize="sm"
      boxShadow="none"
      backgroundColor="transparent"
      borderBottom="2px solid"
      borderColor="fluentHealthSecondary.300"
      _selected={{
        borderColor: 'iris.500',
      }}
    >
      <Flex
        gap="4px"
        justifyContent="center"
        alignItems="center"
      >
        <Text
          fontSize="xl"
          color="gray.500"
        >
          {text}
        </Text>
        {counter && (
          <Flex
            bg="iris.500"
            borderRadius="full"
            alignItems="center"
            justifyContent="center"
            px="4.5px"
            py="2.5px"
            minWidth="16px"
          >
            <Text
              fontSize="11px"
              lineHeight="1"
              color="white"
              fontWeight="700"
            >
              {counter}
            </Text>
          </Flex>
        )}
      </Flex>
    </Tab>
  );
}

// eslint-disable-next-line @typescript-eslint/naming-convention
const ReminderRowSkeleton = forwardRef((props: ChakraProps, ref: LegacyRef<HTMLDivElement>) => {
  return (
    <Flex
      ref={ref}
      px="18px"
      direction="column"
      gap="14px"
      my="14px"
      {...props}
    >
      <Skeleton
        width="144px"
        height="14px"
        borderRadius="6px"
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.500"
        flexShrink={0}
      />
      <Skeleton
        width="80%"
        height="14px"
        borderRadius="6px"
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.500"
        flexShrink={0}
      />
      <Skeleton
        width="60%"
        height="14px"
        borderRadius="6px"
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.500"
        flexShrink={0}
      />
    </Flex>
  );
});
// TODO: NotificationListPanel - Will be pick after Launch

function ReminderListItem({
  alert,
  onEditAlert,
  openRemoveReminderModal,
}: {
  alert: any;
  onEditAlert: (alert: Reminder) => void;
  openRemoveReminderModal: (alert: Reminder) => void;
}) {
  const theme = useTheme();
  const { isExpired, isExpiresSoon, time: alertTime, date: alertDate } = getAlertTime(alert);
  const attachedMedicalRecord = alert?.DocumentReferenceList?.find(
    (docRef: { meta?: { tag?: Array<{ code?: string }> } }) =>
      docRef?.meta?.tag?.some((obj) => (obj?.code ?? '') in MEDICAL_RECORD_NAME_MAP)
  );

  const tag =
    alert?.DocumentReferenceList?.flatMap(
      (docRef: { meta?: { tag?: Array<{ code?: string; system?: string }> } }) => docRef?.meta?.tag
    )?.find(
      (tagObj: { code?: string; system?: string }) =>
        tagObj?.system === FH_UI_CODESYSTEM && (tagObj?.code ?? '') in MEDICAL_RECORD_NAME_MAP
    )?.code ?? '';

  // const alertFrequency = alert?.frequency?.[0]?.extension?.find((item) => item?.url === 'frequency');
  // const isCustomAlert = alertFrequency?.valueCode === CUSTOM_ALERT_FREQUENCY;
  // const isAllDay = alert?.frequency?.[0]?.extension?.find((item) => item?.url === 'isAllDay');

  return (
    <Flex
      gap="8px"
      flexDirection="column"
      padding="12px 20px 20px 20px"
      borderBottom="1px solid"
      borderBottomColor="fluentHealthText.500"
    >
      <Flex
        gap="12px"
        flexDirection="column"
      >
        {attachedMedicalRecord ? (
          <Flex
            gap="10px"
            align="center"
          >
            <Flex
              align="center"
              justify="center"
              width={{ base: '40px', md: '40px' }}
              height={{ base: '40px', md: '40px' }}
              borderRadius="8px"
              bgColor={MEDICAL_RECORD_ICON_BG_MAP[tag]}
            >
              <Box
                maxW="20px"
                height="43px"
              >
                {MEDICAL_RECORD_ICON_MAP[tag as MEDICAL_RECORD_TYPES]}
              </Box>
            </Flex>
            <Flex direction="column">
              <Text fontSize="lg">{alert?.payload?.[0]?.contentAttachment?.title ?? ''}</Text>
              <Flex
                align="center"
                gap="4px"
                color="gray.400"
              >
                <PaperclipIcon
                  size={14}
                  style={{ flexShrink: 0, transform: 'rotateZ(-45deg)', paddingBottom: '2px' }}
                />
                <Text fontSize="sm">{MEDICAL_RECORD_NAME_MAP[tag]}</Text>
              </Flex>
            </Flex>
          </Flex>
        ) : (
          <Flex
            gap="10px"
            align="center"
          >
            <Box
              p="10px"
              borderRadius="8px"
              bgColor={MEDICAL_RECORD_ICON_BG_MAP[MEDICAL_RECORD_TYPES.VACCINE_RECORD]}
            >
              <CalendarIcon color={theme.colors.iris[500]} />
            </Box>
            <Text fontSize="lg"> {alert?.payload?.[0]?.contentString ?? ''}</Text>
          </Flex>
        )}
      </Flex>
      <Flex justifyContent="space-between">
        <Flex
          gap="8px"
          alignItems="center"
        >
          <Box color="fluentHealthText.100">
            {isExpired ? (
              <Flex
                align="center"
                gap="6px"
              >
                <CheckmarkIcon color={theme.colors.gray[500]} />
                <Text>Completed</Text>
              </Flex>
            ) : (
              <Flex
                align="center"
                gap="8px"
                color={isExpiresSoon ? 'fluentHealthComplementary.Red' : 'gray.500'}
              >
                <Clock size="16px" />
                {alertDate}
                <Center height="16px">
                  <Divider
                    orientation="vertical"
                    borderColor="gray.100"
                  />
                </Center>
                {alertTime}
                <Center height="16px">
                  <Divider
                    orientation="vertical"
                    borderColor="gray.100"
                  />
                </Center>
                <Text as="span">
                  {/* {everyField && everyField !== '1'
                  ? customFrequencyField && `Reminder will occur every ${everyField} ${customFrequencyField}.`
                  : `Reminder will occur ${
                      ONCE_IN_FREQUENCY_LABEL_MAP[customFrequencyField as keyof typeof ONCE_IN_FREQUENCY_LABEL_MAP] ||
                      'after ...'
                    }. `} */}
                </Text>
              </Flex>
            )}
          </Box>
        </Flex>
        <MoreActionsMenu>
          <MoreActionsMenuButton iconSize={20} />
          <MoreActionsMenuList>
            <MoreActionsMenuItem
              padding="6px 12px 6px 8px"
              icon={<Edit3 size="16px" />}
              onClick={() => onEditAlert(alert)}
            >
              Edit
            </MoreActionsMenuItem>
            <MoreActionsMenuItem
              padding="6px 12px 6px 8px"
              icon={<Trash size="16px" />}
              onClick={() => openRemoveReminderModal(alert)}
            >
              Remove
            </MoreActionsMenuItem>
          </MoreActionsMenuList>
        </MoreActionsMenu>
      </Flex>
    </Flex>
  );
}

function ReminderListPanel({
  reminderModal,
  removeReminderConfirmationModal,
  calendarModal,
  openReminderModal,
}: {
  reminderModal: UseDisclosureReturn;
  removeReminderConfirmationModal: UseDisclosureReturn;
  calendarModal: UseDisclosureReturn;
  openReminderModal: () => void;
}) {
  const [selectedAlert, setSelectedAlert] = useState<Reminder>();

  const { authenticatedUser } = useAuthService();
  const { alertList, loadingElementRef, hasNextPage, isLoading } = useAlertListLazyPagination(authenticatedUser?.id);
  const { deleteAlert, isMutating: isMutatingAlert } = useAlertList(authenticatedUser?.id);
  const { trackEventInFlow } = useAnalyticsService();

  const onEditAlert = (alert: Reminder) => {
    setSelectedAlert(alert);
    reminderModal.onOpen();
    trackEventInFlow(AnalyticsFlow.ManageReminder, AnalyticsEventName.ManageReminderStarted, {
      [EventPropsNames.FlowName]: EventFlowNames.Navbar,
      [EventPropsNames.Action]: EventFlowActions.Edit,
    });
  };

  const onDeleteAlert = (alert: Reminder) => {
    setSelectedAlert(alert);
    removeReminderConfirmationModal.onOpen();
  };

  const onDeleteAlertConfirmation = async () => {
    await deleteAlert(selectedAlert?.id);
    removeReminderConfirmationModal.onClose();
    setSelectedAlert(undefined);
  };

  const onSetReminderHandler = () => {
    openReminderModal();
  };

  const onTrackReminderHandler = () => {
    openReminderModal();
    trackEventInFlow(AnalyticsFlow.ManageReminder, AnalyticsEventName.ManageReminderStarted, {
      [EventPropsNames.FlowName]: EventFlowNames.Navbar,
      [EventPropsNames.Action]: EventFlowActions.Add,
    });
  };

  // Clear the selected alert after closing modal
  useEffect(() => {
    if (!reminderModal.isOpen) {
      setSelectedAlert(undefined);
    }
  }, [reminderModal.isOpen]);

  return (
    <TabPanel p={0}>
      <ReminderFlow
        selectedAlert={selectedAlert}
        reminderRecordModal={reminderModal}
        calendarModal={calendarModal}
      />
      <Modal
        minWidth="337px"
        modalContentProps={{
          width: '337px',
          py: '32px',
          px: '16px',
        }}
        showModalHeading={false}
        showModalFooter={false}
        showCloseButton={false}
        isCentered
        {...removeReminderConfirmationModal}
      >
        <Flex
          direction="column"
          gap="24px"
        >
          <Box px="8px">
            <Heading
              fontSize="2xl"
              textAlign="center"
              maxWidth="287px"
              mb="16px"
            >
              Are You Sure You Want to Remove this Reminder?
            </Heading>
            <Text textAlign="center">This cannot be undone.</Text>
          </Box>
          <Flex
            flexDirection="row"
            justifyContent="center"
            alignItems="center"
            gap="16px"
          >
            <Button
              variant="ghost"
              width="120px"
              isLoading={isMutatingAlert}
              onClick={onDeleteAlertConfirmation}
            >
              Remove
            </Button>
            <Button
              padding="16px 36px"
              width="159px"
              onClick={removeReminderConfirmationModal.onClose}
            >
              Cancel
            </Button>
          </Flex>
        </Flex>
      </Modal>
      <TabPanel padding={0}>
        {alertList.length === 0 && !isLoading ? (
          <Flex
            justifyContent="center"
            alignItems="center"
            bg="periwinkle.100"
            height="475px"
            flexDirection="column"
            gap="32px"
            padding="20px"
            margin="16px"
            borderRadius="8px"
          >
            <EmptyStateIcon />
            <Text
              color="iris.400"
              fontSize="lg"
              width="240px"
              textAlign="center"
            >
              You don&apos;t have any reminders set at the moment
            </Text>
            <Button
              padding="12px 24px"
              fontSize="lg"
              onClick={onSetReminderHandler}
            >
              Set Reminder
            </Button>
          </Flex>
        ) : (
          <>
            <Flex
              padding="8px 20px"
              justifyContent="space-between"
              alignItems="center"
              borderBottom="1px solid"
              borderBottomColor="fluentHealthText.500"
            >
              <Text color="fluentHealthText.300">Latest</Text>
              <Button
                variant="quiet"
                color="iris.500"
                fontSize="md"
                fontWeight="400"
                height="auto"
                onClick={onTrackReminderHandler}
              >
                Set Reminder
              </Button>
            </Flex>
            <Flex
              flexDirection="column"
              maxHeight={{ base: 'auto', md: '600px' }}
              overflowY="auto"
              className="hide-scrollbar"
            >
              {isLoading && <FluentHealthLoader py="62px" />}
              {alertList.map((alert: any) => (
                <ReminderListItem
                  key={alert?.id}
                  alert={alert}
                  onEditAlert={onEditAlert}
                  openRemoveReminderModal={onDeleteAlert}
                />
              ))}
              {hasNextPage && <ReminderRowSkeleton ref={loadingElementRef} />}
            </Flex>
          </>
        )}
      </TabPanel>
    </TabPanel>
  );
}

export function Notifications() {
  const disclosure = useNotificationDisclosure((state) => state, shallow);
  // const [notificationCount, setNotificationCount] = useState(0);

  const reminderModal = useDisclosure();
  const removeReminderConfirmationModal = useDisclosure();
  const calendarModal = useDisclosure();
  const popoverContainerRef = useRef<HTMLDivElement | null>(null);
  useOutsideClick({
    ref: popoverContainerRef,
    handler: () => {
      if (!reminderModal.isOpen && !removeReminderConfirmationModal.isOpen && !calendarModal.isOpen) {
        disclosure.onClose();
      }
      return true;
    },
  });

  const updateNotificationCount = () => {
    // const notificationsCount: number = clevertap.getInboxMessageUnreadCount() ?? 0;
    // setNotificationCount(notificationsCount);
  };

  useEffect(() => {
    updateNotificationCount(); // Fetch and set the notification count on mount

    // If `clevertap` has a subscription/event for notification updates, manage it here
    const intervalId = setInterval(() => {
      updateNotificationCount(); // Periodically update count if needed
    }, 500);

    return () => {
      clearInterval(intervalId); // Cleanup on component unmount
    };
  }, []);

  return (
    <Box ref={popoverContainerRef}>
      <Popover
        returnFocusOnClose={false}
        isOpen={disclosure.isOpen}
        onClose={disclosure.onClose}
        offset={[0, 20]}
        isLazy
        closeOnBlur={false}
      >
        <PopoverTrigger>
          <IconButton
            aria-label="notifications"
            backgroundColor="transparent"
            color="fluentHealthSecondary.200"
            minW="unset"
            size="md"
            p="8px"
            onClick={disclosure.onToggle}
            _hover={{
              backgroundColor: 'periwinkle.200',
              color: 'periwinkle.700',
            }}
            icon={<BellIcon size="20px" />}
          />
        </PopoverTrigger>
        <PopoverContent
          maxW="560px"
          width="100vw"
          borderRadius="20px"
          borderColor="fluentHealthSecondary.300"
          display="flex"
          flexDirection="column"
          boxShadow="0px 10px 28px -2px rgba(7, 16, 84, 0.14), 0px 1px 4px 0px rgba(7, 16, 84, 0.10)"
          right={{ base: '0', sm: '12px' }}
        >
          <Tabs
            pt="12px"
            index={disclosure.tabIndex}
            onChange={disclosure.setTabIndex}
          >
            <TabList
              display="flex"
              justifyContent="space-between"
              lineHeight="5"
              fontWeight="normal"
            >
              {/* TODO: Notification - Will be pick after Launch */}
              <NotificationTab text="Reminders" />
            </TabList>
            <TabPanels>
              <Suspense fallback={<FluentHealthLoader py="62px" />}>
                <ReminderListPanel
                  reminderModal={reminderModal}
                  removeReminderConfirmationModal={removeReminderConfirmationModal}
                  openReminderModal={reminderModal.onOpen}
                  calendarModal={calendarModal}
                />
              </Suspense>
            </TabPanels>
          </Tabs>
        </PopoverContent>
      </Popover>
    </Box>
  );
}

export function MobileNotifications({
  drawerDisclosure,
  menuDisclosure,
}: {
  drawerDisclosure: UseDisclosureReturn;
  menuDisclosure: UseDisclosureReturn;
}) {
  // const [mobileNotificationCount, setMobileNotificationCount] = useState(0); // State to store the count

  const disclosure = useNotificationDisclosure((state) => state, shallow);

  const reminderModal = useDisclosure();
  const removeReminderConfirmationModal = useDisclosure();
  const calendarModal = useDisclosure();
  const { DASHBOARD } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;

  const menuHandler = () => {
    menuDisclosure.onOpen();
    drawerDisclosure.onClose();
  };

  // TODO: we need to update the notification count for mobile and web too cause repeated code here

  const updateNotificationCount = () => {
    // const notificationsCount: number = clevertap.getInboxMessageUnreadCount() ?? 0;
    // setMobileNotificationCount(notificationsCount);
  };

  useEffect(() => {
    updateNotificationCount(); // Fetch and set the notification count on mount

    // If `clevertap` has a subscription/event for notification updates, manage it here
    const intervalId = setInterval(() => {
      updateNotificationCount(); // Periodically update count if needed
    }, 500);

    return () => {
      clearInterval(intervalId); // Cleanup on component unmount
    };
  }, []);

  return (
    <Drawer
      isOpen={drawerDisclosure.isOpen}
      onClose={drawerDisclosure.onClose}
      size="full"
    >
      <DrawerOverlay />
      <DrawerContent>
        <DrawerBody
          bg="linear-gradient(346deg, #DADCFF 0%, #FFF2DF 100%)"
          paddingX="0px"
          paddingTop="0px"
          className="hide-scrollbar"
        >
          <Flex
            paddingLeft="16px"
            paddingRight="14px"
            justifyContent="space-between"
            alignItems="center"
            height="55px"
          >
            <Link
              to={`/${DASHBOARD}/${VIEW}`}
              onClick={drawerDisclosure.onClose}
            >
              <FluentHealthLogo />
            </Link>
            <IconButton
              aria-label="Open menu"
              backgroundColor="transparent"
              _hover={{
                backgroundColor: 'transparent',
              }}
              onClick={menuHandler}
              icon={<HamburgerMenu />}
            />
          </Flex>

          <Tabs
            pt="24px"
            index={disclosure.tabIndex}
            onChange={disclosure.setTabIndex}
          >
            <TabList
              display="flex"
              justifyContent="space-between"
              lineHeight="5"
              fontWeight="normal"
            >
              {/* TODO: Notification - Will be pick after Launch */}
              <NotificationTab text="Reminders" />
            </TabList>
            <TabPanels>
              <Suspense fallback={<FluentHealthLoader py="62px" />}>
                <ReminderListPanel
                  reminderModal={reminderModal}
                  removeReminderConfirmationModal={removeReminderConfirmationModal}
                  openReminderModal={reminderModal.onOpen}
                  calendarModal={calendarModal}
                />
              </Suspense>
            </TabPanels>
          </Tabs>
        </DrawerBody>
      </DrawerContent>
    </Drawer>
  );
}
