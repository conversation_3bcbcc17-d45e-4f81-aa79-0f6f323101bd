/* eslint-disable complexity */
// Package modules
import {
  Avatar,
  Box,
  Button,
  ChakraProps,
  Drawer,
  DrawerBody,
  DrawerContent,
  DrawerOverlay,
  Flex,
  HStack,
  Heading,
  IconButton,
  Image,
  Link,
  Spacer,
  Text,
  UseDisclosureReturn,
  useDisclosure,
} from '@chakra-ui/react';
import dayjs from 'dayjs';
import dayjsDurationPlugin from 'dayjs/plugin/duration';
import jwtDecode from 'jwt-decode';
import { Settings as SettingsIcon, User as UserIcon } from 'react-feather';
import { NavLink, useLocation, useSearchParams } from 'react-router-dom';
// Local modules
import { useEffect, useState } from 'react';
import { recordProfileEvents, recordSettingsEvents } from '@user/lib/events-analytics-manager';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { AuthService } from '@lib/authService';
import { useAnalyticsService, useAuthService, usePublicRecordSettings, usePublicSettings } from '@lib/state';
import { parsePatientName, pluralize } from '@lib/utils/utils';
// import { usePatient } from '../app/user/lib/state';
import { usePatient } from '../app/user/lib/medplum-state';
import { useIsMobile } from './ui/hooks/device.hook';
import { MobileNotifications, Notifications } from './Notifications';
import { hexOpacity } from './theme/utils';
import { cleverTapEventId } from '@lib/constants';

// Assets

import { ReactComponent as FluentHealthLogo } from '@assets/icons/fh-logo.svg';
import { ReactComponent as FluentHealthWhiteLogo } from '@assets/objects/fh-white-logo.svg';
import { ReactComponent as HamburgerMenu } from '@assets/objects/hamburger-menu.svg';
import { ReactComponent as CloseIcon } from '@assets/icons/mobile-close-button.svg';

dayjs.extend(dayjsDurationPlugin);

const { DASHBOARD, PROFILE, BASIC, DOCUMENTS, SETTINGS, PERSONAL } = ROUTE_VARIABLES;
const { VIEW } = ROUTE_ACTIONS;

const LINKS = {
  PROFILE: { route: `/${PROFILE}/${BASIC}/${VIEW}`, label: 'My Account' },
  HOME: { route: `/${DASHBOARD}/${VIEW}`, label: 'Home' },
  MEDICAL_RECORDS: { route: `/${DOCUMENTS}/${PERSONAL}/${VIEW}`, label: 'Medical Records' },
  SETTINGS: { route: `/${SETTINGS}`, label: 'Settings' },
  // TODO: Health Graphs - Will be pick after Launch
};

const getRemainingTime = (exp: string) => {
  const IST_OFFSET_MINUTES = 330;
  const initDate = dayjs();
  // Adjust the exp time back by 5.5 hours (330 minutes) to reverse incorrect IST epoch shift
  const expDate = dayjs((parseInt(exp, 10) - IST_OFFSET_MINUTES * 60) * 1000);

  const delta = dayjs.duration(expDate.diff(initDate));

  // calculate (and subtract) whole days
  const days = delta.days();

  // calculate (and subtract) whole hours
  const hours = delta.hours();

  // calculate (and subtract) whole minutes
  const minutes = delta.minutes();

  const x = `${days ? days + pluralize(' day', days) : ''}  ${hours ? hours + pluralize(' hour', hours) : ''} ${
    minutes ? minutes + pluralize(' minute', minutes) : ''
  }`;

  return x;
};

// const remainingTimeToStringTime = (remainingTime: plugin.Duration): string => {
//   let result = '';
//   const months = remainingTime.months();
//   const days = remainingTime.days();
//   const hours = remainingTime.hours();
//   const minutes = remainingTime.minutes();

//   if (months > 0) {
//     result = `${months} ${pluralize('month', months)}`;
//   } else if (days > 0) {
//     result = `${days} ${pluralize('day', days)}`;
//   } else if (hours > 0) {
//     result = `${hours} ${pluralize('hour', hours)}`;
//   } else {
//     result = `${minutes} ${pluralize('minute', minutes)}`;
//   }

//   return result;
// };

function HighlightedBorder(props: ChakraProps) {
  return (
    <Box
      backgroundColor="fluentHealth.500"
      height="1px"
      width="100%"
      position="absolute"
      bottom="-14px"
      left="50%"
      transform="translate(-50%)"
      borderRadius="3"
      {...props}
    />
  );
}

const MOBILE_LINKS = [
  { name: 'HOME', route: `/${DASHBOARD}/${VIEW}`, label: 'Home' },
  { name: 'MEDICAL_RECORDS', route: `/${DOCUMENTS}/${VIEW}`, label: 'Health records' },
  { name: 'PROFILE', route: `/${PROFILE}/${BASIC}/${VIEW}`, label: 'Health profile' },
  { name: 'NOTIFICATIONS', route: '', label: 'Notifications' },
  { name: 'SETTINGS', route: `/${SETTINGS}`, label: 'Settings' },
];

export function MobileMenu({ drawerDisclosure }: { drawerDisclosure: UseDisclosureReturn }) {
  const mobileNotifications = useDisclosure();
  const { trackEventInFlow } = useAnalyticsService();
  const notificationDrawerHandler = () => {
    drawerDisclosure.onClose();
    mobileNotifications.onOpen();
  };

  return (
    <>
      <Drawer
        {...drawerDisclosure}
        size="full"
      >
        <DrawerOverlay />
        <DrawerContent bg="iris.500">
          <DrawerBody paddingY="20px">
            <Flex
              justifyContent="space-between"
              height="52px"
              alignItems="center"
            >
              <Link
                as={NavLink}
                to={`/${DASHBOARD}/${VIEW}`}
                onClick={drawerDisclosure.onClose}
              >
                <FluentHealthWhiteLogo />
              </Link>
              <IconButton
                aria-label="Close menu"
                zIndex="var(--chakra-zIndices-popover)"
                icon={<CloseIcon />}
                bg="transparent"
                _hover={{ bg: 'transparent' }}
                onClick={drawerDisclosure.onClose}
              />
            </Flex>
            <Flex
              direction="column"
              mt="52px"
              alignItems="center"
              gap="24px"
            >
              {MOBILE_LINKS.map((link) => (
                <Flex
                  key={link.label}
                  paddingX="32px"
                  paddingY="9px"
                >
                  {link.name === 'NOTIFICATIONS' ? (
                    <Link
                      as={NavLink}
                      to={link.route}
                      _hover={{ textDecoration: 'none' }}
                      onClick={notificationDrawerHandler}
                      display="flex"
                      alignItems="center"
                    >
                      <Heading
                        color="white"
                        fontSize="32px"
                      >
                        {link.label}
                      </Heading>
                    </Link>
                  ) : (
                    <Link
                      as={NavLink}
                      to={link.route}
                      _hover={{ textDecoration: 'none' }}
                      onClick={drawerDisclosure.onClose}
                      display="flex"
                      alignItems="center"
                      onClickCapture={() => {
                        const events: { [key: string]: () => void } = {
                          SETTINGS: () => recordSettingsEvents(trackEventInFlow, { EventName: 'SettingsClicked' }),
                          PROFILE: () => recordProfileEvents(trackEventInFlow, { EventName: 'MyProfileInteracted' }),
                        };
                        events[link.name]?.();
                      }}
                    >
                      <Heading
                        color="white"
                        fontSize="32px"
                      >
                        {link.label}
                      </Heading>
                    </Link>
                  )}
                </Flex>
              ))}
            </Flex>
            <Flex
              justifyContent="center"
              mt="60px"
            >
              <Button
                bg="papaya.600"
                padding="12px 24px"
                fontSize="lg"
                height="50px"
                _hover={{
                  bg: 'papaya.600',
                }}
              >
                Download App
              </Button>
            </Flex>
          </DrawerBody>
        </DrawerContent>
      </Drawer>
      <MobileNotifications
        drawerDisclosure={mobileNotifications}
        menuDisclosure={drawerDisclosure}
      />
    </>
  );
}

export function Navbar() {
  const { isLoggedIn } = useAuthService();
  const { patient } = usePatient();
  const { pathname: currentRoute } = useLocation();
  const isMobile = useIsMobile();
  const { isPublicMode } = usePublicSettings();
  const { isPublicRecordMode } = usePublicRecordSettings();
  const [searchParams] = useSearchParams();
  const fullScreenMobileMenu = useDisclosure();
  const [formattedRemainingTime, setFormattedRemainingTime] = useState<string>('');
  const { trackEventInFlow } = useAnalyticsService();
  useEffect(() => {
    if (isPublicMode || isPublicRecordMode) {
      const tempToken = searchParams.get('access_token');
      const decodedToken: { exp: string } = jwtDecode(tempToken!);
      const remainingTime = getRemainingTime(decodedToken.exp);
      setFormattedRemainingTime(remainingTime);
    }
  }, [isPublicMode, isPublicRecordMode]);
  const isProfilePage = currentRoute.startsWith(LINKS.PROFILE.route);
  const userName = `${
    patient?.name?.[0]?.given && patient?.name?.[0]?.given?.length ? patient?.name?.[0]?.given?.[0] : '--'
  }`;
  const avatarUrl = !isPublicMode && !isPublicRecordMode && patient?.photo?.length ? patient.photo[0].url : undefined;

  return (
    <Box
      position="sticky"
      top={0}
      zIndex={2}
    >
      <Flex
        alignItems="center"
        justifyContent="space-between"
        backgroundColor={{ base: hexOpacity('#ffffff', 0.4), md: 'white' }}
        py="10px"
        px={{ base: '3.5', md: '9' }}
        borderBottom="1px solid"
        borderColor={{ base: 'transparent', md: 'fluentHealthSecondary.300' }}
        backdropFilter={{ base: 'blur(20px)', md: 'none' }}
        {...(!isLoggedIn && { p: 5 })}
      >
        <HStack>
          <FluentHealthLogo />
          {!isPublicMode && !isPublicRecordMode && !isMobile && isLoggedIn && (
            <Flex gap="2">
              <HStack marginLeft="8">
                {[LINKS.HOME, LINKS.MEDICAL_RECORDS].map(({ route, label }) => (
                  <Link
                    as={NavLink}
                    key={route}
                    opacity="0.9"
                    textTransform="uppercase"
                    letterSpacing="widest"
                    py="2"
                    px="4"
                    borderRadius="3xl"
                    transition="all .3s ease"
                    color="fluentHealth.500"
                    fontFamily="Apercu"
                    fontSize="sm"
                    fontWeight="semibold"
                    borderBottomRadius={route === currentRoute ? 'sm' : ''}
                    _hover={{
                      md: {
                        transition: 'all .3s ease',
                        ...(route !== currentRoute && { backgroundColor: 'fluentHealthSecondary.50' }),
                      },
                    }}
                    position="relative"
                    to={route}
                  >
                    {label}
                    {route === currentRoute && <HighlightedBorder />}
                  </Link>
                ))}
              </HStack>
            </Flex>
          )}
        </HStack>
        {isLoggedIn && <Spacer height="0" />}
        <Flex
          alignItems="center"
          justifyContent="space-between"
          gap="8"
        >
          <Flex
            align="center"
            gap="12px"
          >
            {!isPublicMode && !isPublicRecordMode && !isMobile && isLoggedIn && (
              <>
                <Notifications />
                <Link
                  as={NavLink}
                  to={LINKS.SETTINGS.route}
                  position="relative"
                  onClickCapture={() =>
                    recordSettingsEvents(trackEventInFlow, {
                      EventName: 'SettingsClicked',
                    })
                  }
                >
                  <IconButton
                    aria-label={LINKS.SETTINGS.label}
                    backgroundColor="transparent"
                    color={
                      currentRoute.startsWith(LINKS.SETTINGS.route) ? 'periwinkle.700' : 'fluentHealthSecondary.200'
                    }
                    minW="unset"
                    size="md"
                    p="8px"
                    icon={<SettingsIcon size="20px" />}
                    _hover={{
                      backgroundColor: 'periwinkle.200',
                      color: 'periwinkle.700',
                    }}
                  />
                  {currentRoute.startsWith(LINKS.SETTINGS.route) && <HighlightedBorder bottom="-15px" />}
                </Link>
                <i
                  style={{ display: 'none' }}
                  id={cleverTapEventId}
                />
              </>
            )}
            {(isPublicMode || isPublicRecordMode) && (
              <>
                <Text
                  userSelect="none"
                  fontSize="sm"
                  lineHeight="xl"
                  color="fluentHealthSecondary.200"
                >
                  Link expires in {formattedRemainingTime}
                </Text>
                <Text
                  fontWeight="bold"
                  userSelect="none"
                  fontFamily="Apercu"
                  fontSize="sm"
                  lineHeight="xl"
                  color="fluentHealthText.100"
                >
                  {userName}
                </Text>
              </>
            )}
            {!isLoggedIn && (
              <Avatar
                src="/share-profile-pic.png"
                width="40px"
                height="40px"
                bgColor="fluentHealthSecondary.300"
              />
            )}
            {!isMobile && isLoggedIn && (
              <Link
                as={NavLink}
                to={!isPublicMode && !isPublicRecordMode ? LINKS.PROFILE.route : '#'}
                position="relative"
                ml="6px"
                cursor={!isPublicMode && !isPublicRecordMode ? 'pointer' : 'default'}
                onClickCapture={() =>
                  recordProfileEvents(trackEventInFlow, {
                    EventName: 'MyProfileInteracted',
                  })
                }
              >
                <Avatar
                  position="relative"
                  pointerEvents="all"
                  transition="outline .3s ease"
                  src={avatarUrl}
                  width="44px"
                  height="44px"
                  loading="lazy"
                  bgColor="fluentHealthSecondary.300"
                  _hover={{
                    _before: {
                      borderColor:
                        !isPublicMode && !isPublicRecordMode
                          ? `fluentHealthSecondary.${isProfilePage ? '150' : '200'}`
                          : 'transparent',
                    },
                  }}
                  _before={{
                    content: `""`,
                    position: 'absolute',
                    top: '-3px',
                    right: '-3px',
                    bottom: '-3px',
                    left: '-3px',
                    border: '1px solid',
                    borderColor:
                      isProfilePage && !isPublicMode && !isPublicRecordMode ? 'fluentHealth.500' : 'transparent',
                    borderRadius: '100%',
                    transition: 'border-color .3s ease',
                  }}
                />
                {isProfilePage && !isPublicMode && !isPublicRecordMode && <HighlightedBorder bottom="-11px" />}
              </Link>
            )}
            {isMobile && isLoggedIn && (
              <IconButton
                aria-label="Open menu"
                backgroundColor="transparent"
                _hover={{
                  backgroundColor: 'transparent',
                }}
                icon={<HamburgerMenu />}
                onClick={fullScreenMobileMenu.onOpen}
              />
            )}
            <MobileMenu drawerDisclosure={fullScreenMobileMenu} />
          </Flex>
        </Flex>
      </Flex>
    </Box>
  );
}

export function PublicNavbar() {
  const isMobile = useIsMobile();
  const { isPublicMode } = usePublicSettings();
  const { isPublicRecordMode } = usePublicRecordSettings();
  // const isPublicMode = true;
  const { authenticatedUser: patient = null } = useAuthService();
  const [expirationMessage, setExpirationMessage] = useState<string>('');

  const { temporaryToken } = AuthService;
  const decodedToken: { exp: string } = jwtDecode(temporaryToken!);
  const remainingTime = getRemainingTime(decodedToken.exp);
  setExpirationMessage(`Link expires in ${remainingTime}`);

  const parsedPatientName = parsePatientName(patient.name, '');
  const patientName = parsedPatientName.length > 0 ? parsedPatientName.split(' ')[0] : 'No name';
  const patientPhoto = patient.photo?.[0].url ?? undefined;

  return (
    <Box>
      <Flex
        alignItems="center"
        justifyContent="space-between"
        backgroundColor="white"
        py="10px"
        px="9"
        {...(!isPublicMode && !isPublicRecordMode
          ? {
              borderBottom: '1px solid',
              borderColor: 'fluentHealthSecondary.300',
            }
          : {})}
        gap="10px"
      >
        <FluentHealthLogo />
        {isMobile && (
          <Text
            color="iris.300"
            textAlign="center"
            lineHeight="16px"
            fontSize="sm"
          >
            {expirationMessage}
          </Text>
        )}
        <Flex
          align="center"
          gap="24px"
        >
          {!isMobile && <Text color="iris.300">{expirationMessage}</Text>}

          <Flex
            align="center"
            gap="12px"
          >
            <Text
              color="fluentHealthText.100"
              fontSize={{ base: 'md', md: 'lg' }}
            >
              {patientName}
            </Text>
            <Flex
              align="center"
              justify="center"
              w="40px"
              h="40px"
              rounded="full"
              backgroundColor="periwinkle.200"
              color="iris.300"
            >
              <Image
                src={patientPhoto}
                fallback={<UserIcon size={24} />}
                objectFit="fill"
                width="full"
                height="full"
                rounded="full"
              />
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </Box>
  );
}
