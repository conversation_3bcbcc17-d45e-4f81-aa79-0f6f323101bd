import { useRef, useState } from 'react';
import { Column, flexRender, Table } from '@tanstack/react-table';
import { cva, VariantProps } from 'class-variance-authority';
import clsx from 'clsx';
import { useSearchParams } from 'react-router-dom';

import { useNavigateSearch } from '../hooks/navigate-search.hook';

const styles = cva('w-full text-sm font-medium', {
  variants: {},
  defaultVariants: {},
});

export type TappTableProps = { table: Table<any>; stickyHeader?: boolean } & VariantProps<typeof styles> &
  JSX.IntrinsicElements['table'];

export const TappTable: React.FC<TappTableProps> = ({ table, stickyHeader = false, className, ...props }) => {
  const [scrolled, setScrolled] = useState(false);
  const tableRef = useRef<HTMLTableElement>(null);
  const theadRef = useRef<HTMLTableSectionElement>(null);
  const [searchParams] = useSearchParams();
  const navigate = useNavigateSearch();

  // TODO: Sticky is not working for now
  // This checks if the thead is "sticked" to the scroll container so that
  // we can apply a shadow to it while scrolling
  // useEffect(() => {
  //   if (stickyHeader && theadRef.current) {
  //     const cachedRef = theadRef.current;
  //     const observer = new IntersectionObserver(([e]) => setScrolled(e.intersectionRatio < 1), {
  //       threshold: [1],
  //     });
  //     observer.observe(cachedRef);
  //     return function () {
  //       observer.unobserve(cachedRef);
  //     };
  //   }
  // }, [theadRef, stickyHeader]);

  // Set the sorting params when clicking on a column header
  const setSorting = (column: Column<any>) => {
    if (!column.getCanSort()) return;
    column.toggleSorting();
    const newSortField = column.id;
    const newSortDirection = column.getNextSortingOrder() || null;
    navigate({
      params: {
        sortField: newSortDirection ? newSortField : null,
        sortDirection: newSortDirection,
      },
      paramsHandling: 'merge',
    });
  };

  return (
    // TODO: Move this wrapper in table wrapper component
    <div className="w-full overflow-x-auto">
      <table
        ref={tableRef}
        className={clsx(styles({}), className)}
        {...props}
      >
        <thead
          ref={theadRef}
          className={clsx({ 'sticky -top-px': stickyHeader, 'shadow-md': scrolled }, 'font-semibold transition')}
        >
          {table.getHeaderGroups().map((headerGroup) => (
            <tr
              key={headerGroup.id}
              className=""
            >
              {headerGroup.headers.map((header) => (
                <th
                  key={header.id}
                  className="h-12 bg-gray-50 px-4 text-start text-sm font-semibold first-of-type:rounded-tl last-of-type:rounded-tr"
                >
                  {header.isPlaceholder ? null : (
                    <div
                      className={clsx('flex items-center', {
                        'cursor-pointer select-none': header.column.getCanSort(),
                      })}
                      onClick={() => setSorting(header.column)}
                    >
                      {flexRender(header.column.columnDef.header, header.getContext())}{' '}
                      {searchParams.get('sortField') === header.column.id && 'arrow_drop_up'}
                    </div>
                  )}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody className="divide-y-gray-200 divide-y">
          {table.getRowModel().rows.map((row, rowIdx) => (
            <tr
              key={row.id}
              className={clsx('group h-12')}
            >
              {row.getVisibleCells().map((cell, cellIdx) => (
                <td
                  key={cell.id}
                  className={clsx(
                    'bg-white py-3 px-4 group-last-of-type:first-of-type:rounded-bl group-last-of-type:last-of-type:rounded-br'
                  )}
                >
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
