// Package imports
import React from 'react';
import { NavLink } from 'react-router-dom';
import { Box, Flex, Text } from '@chakra-ui/react';

// Local imports
import { usePublicRecordSettings, usePublicSettings } from '@lib/state';

const links = [
  {
    id: 1,
    text: 'Privacy Policy',
    to: '/privacy-policy/view',
  },
  {
    id: 2,
    text: 'Terms & Conditions',
    to: '/terms-n-conditions/view',
  },
];

function Footer() {
  const { isPublicMode } = usePublicSettings();
  const { isPublicRecordMode } = usePublicRecordSettings();

  return (
    <Flex
      direction="column"
      mt="auto"
    >
      {isPublicMode ||
        (isPublicRecordMode && (
          <Box
            px="48px"
            pb="24px"
            mt="90px"
            textAlign="center"
          >
            This information/record(s) have been generated by the user based on information/records entered by the user
            in the App and may contain errors. Be Fluent LLP makes no representation that this information/record(s)
            is/are complete; it does not guarantee its accuracy and shall not be liable or responsible in any manner for
            errors or interpretations of the data within it. This record is private, confidential, and intended solely
            for the individual to whom it pertains and the individual/entity to whom it has been addressed. If you have
            received this record in error, notify the sender and do not disclose, copy, divulge or use this report or
            any information contained herein.
          </Box>
        ))}
      <Flex
        bg="iris.500"
        padding="20px"
        width="100%"
        gap={{ base: '24px', md: '40px' }}
        direction={{ base: 'column-reverse', md: 'row' }}
        justifyContent="center"
        alignItems={{ base: 'center', md: 'start' }}
      >
        <Text color="iris.200">Fluent Health {new Date().getFullYear()} © All Rights Reserved</Text>
        <Flex gap="24px">
          {links.map((link) => (
            <NavLink
              key={link.id}
              to={link.to}
            >
              <Text
                color="white"
                fontWeight="500"
              >
                {link.text}
              </Text>
            </NavLink>
          ))}
        </Flex>
      </Flex>
    </Flex>
  );
}

export default Footer;
