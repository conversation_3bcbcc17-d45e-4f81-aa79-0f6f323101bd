<svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <rect width="90" height="90" fill="#F5F5F5"/>
    <path d="M-1030 -675C-1030 -719.183 -994.183 -755 -950 -755H12982C13026.2 -755 13062 -719.183 13062 -675V962C13062 1006.18 13026.2 1042 12982 1042H-950C-994.183 1042 -1030 1006.18 -1030 962V-675Z" fill="#EAEAEA"/>
    <g clip-path="url(#clip0_15_8314)">
        <rect width="1440" height="920" transform="translate(-930 -655)" fill="url(#paint0_linear_15_8314)"/>
        <ellipse cx="470" cy="559.026" rx="806" ry="820.893" fill="#495AE4" fill-opacity="0.2"/>
        <g clip-path="url(#clip1_15_8314)">
            <rect x="-526" y="-15" width="632" height="120" rx="20" fill="#4956E4"/>
            <circle opacity="0.1" cx="-36" cy="-112" r="185" fill="white"/>
            <rect width="90" height="90" rx="10" fill="white"/>
            <rect width="90" height="90" fill="url(#pattern0)"/>
        </g>
    </g>
    <defs>
        <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
            <use xlink:href="#image0_15_8314" transform="scale(0.00337838)"/>
        </pattern>
        <linearGradient id="paint0_linear_15_8314" x1="291.84" y1="-15.5405" x2="281.777" y2="920.057" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FFEFD8"/>
            <stop offset="1" stop-color="#CED1FF"/>
        </linearGradient>
        <clipPath id="clip0_15_8314">
            <rect width="1440" height="920" fill="white" transform="translate(-930 -655)"/>
        </clipPath>
        <clipPath id="clip1_15_8314">
            <rect x="-526" y="-15" width="632" height="120" rx="20" fill="white"/>
        </clipPath>
        <image id="image0_15_8314" width="296" height="296" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASgAAAEoCAYAAADrB2wZAAAMPmlDQ1BJQ0MgUHJvZmlsZQAASImVVwdYU8kWnluSkEAIEEBASuhNEKkBpITQQu8INkISIJQYA0HFji4quHYRARu6KqJgBcSO2FkUe18sqCjrYsGuvEkBXfeV702+ufPnnzP/OXNmbhkAaMe5YnEuqgFAnqhAEhcSwBiTksogPQUo0AWq8Efj8vLFrJiYCADLYPv38u46QGTtFQeZ1j/7/2vR5AvyeQAgMRCn8/N5eRDvBwCv5oklBQAQZbz5lAKxDMMKtCUwQIgXynCmAlfLcLoC75bbJMSxIW4DQEWNy5VkAqB+CfKMQl4m1FDvg9hJxBeKAKAxIPbNy5vEhzgNYhtoI4ZYps9M/0En82+a6UOaXG7mEFbMRV5UAoX54lzutP8zHf+75OVKB31YwaqWJQmNk80Z5u1mzqRwGVaDuFeUHhUNsRbEH4R8uT3EKCVLGpqosEcNeflsmDO40gB14nMDwyE2hDhYlBsVoeTTM4TBHIjhDkGnCgs4CRDrQbxQkB8Ur7TZKJkUp/SFNmZI2Cwlf5YrkfuV+bovzUlkKfVfZwk4Sn1MvSgrIRliCsQWhcKkKIjVIXbMz4kPV9qMLspiRw3aSKRxsvgtII4TiEICFPpYYYYkOE5pX5qXPzhfbGOWkBOlxHsLshJCFfnB2nhcefxwLtglgYiVOKgjyB8TMTgXviAwSDF37JlAlBiv1PkgLgiIU4zFKeLcGKU9bibIDZHxZhC75hfGK8fiSQVwQyr08QxxQUyCIk68KJsbFqOIB18GIgAbBAIGkMKaDiaBbCDs6G3qhf8UPcGACyQgEwiAg5IZHJEs7xHBazwoAn9CJAD5Q+MC5L0CUAj5r0Os4uoAMuS9hfIROeAJxHkgHOTC/1L5KNGQtyTwGDLCf3jnwsqD8ebCKuv/9/wg+51hQSZCyUgHPTJog5bEIGIgMZQYTLTFDXBf3BuPgFd/WJ1xJu45OI/v9oQnhE7CQ8I1Qhfh1kRhseSnKCNBF9QPVuYi/cdc4FZQ0w0PwH2gOlTGdXED4IC7Qj8s3A96doMsWxm3LCuMn7T/NoMfVkNpR3Yio+RhZH+yzc8j1e3U3YZUZLn+MT+KWNOH8s0e6vnZP/uH7PNhG/6zJbYQ24edwU5g57DDWBNgYMewZqwdOyLDQ7vrsXx3DXqLk8eTA3WE//A3uLKyTOY71Tn1OH1R9BUIpsqe0YA9STxNIszMKmCw4BtBwOCIeI4jGM5Ozi4AyN4visfXm1j5ewPRbf/OzfsDAJ9jAwMDh75zYccA2OMBb/+D3zkbJnx1qAJw9iBPKilUcLjsQoBPCRq80/SBMTAHNnA+zsAdeAN/EATCQDRIAClgAow+C+5zCZgCZoC5oASUgWVgNagEG8BmsB3sAntBEzgMToDT4AK4BK6BO3D3dIMXoA+8A58RBCEhVISO6CMmiCVijzgjTMQXCUIikDgkBUlDMhERIkVmIPOQMmQFUolsQmqRPchB5ARyDulEbiEPkB7kNfIJxVA1VBs1Qq3QkSgTZaHhaAI6Hs1EJ6NF6Hx0CVqB1qA70Ub0BHoBvYZ2oS/QfgxgqpguZoo5YEyMjUVjqVgGJsFmYaVYOVaD1WMtcJ2vYF1YL/YRJ+J0nIE7wB0ciifiPHwyPgtfjFfi2/FGvA2/gj/A+/BvBCrBkGBP8CJwCGMImYQphBJCOWEr4QDhFLyXugnviESiLtGa6AHvxRRiNnE6cTFxHbGBeJzYSXxE7CeRSPoke5IPKZrEJRWQSkhrSTtJx0iXSd2kDyqqKiYqzirBKqkqIpVilXKVHSpHVS6rPFX5TNYgW5K9yNFkPnkaeSl5C7mFfJHcTf5M0aRYU3woCZRsylxKBaWecopyl/JGVVXVTNVTNVZVqDpHtUJ1t+pZ1QeqH9W01OzU2Grj1KRqS9S2qR1Xu6X2hkqlWlH9qanUAuoSai31JPU+9YM6Xd1RnaPOV5+tXqXeqH5Z/SWNTLOksWgTaEW0cto+2kVarwZZw0qDrcHVmKVRpXFQ44ZGvyZdc5RmtGae5mLNHZrnNJ9pkbSstIK0+FrztTZrndR6RMfo5nQ2nUefR99CP0Xv1iZqW2tztLO1y7R3aXdo9+lo6bjqJOlM1anSOaLTpYvpWulydHN1l+ru1b2u+2mY0TDWMMGwRcPqh10e9l5vuJ6/nkCvVK9B75reJ32GfpB+jv5y/Sb9ewa4gZ1BrMEUg/UGpwx6h2sP9x7OG146fO/w24aooZ1hnOF0w82G7Yb9RsZGIUZio7VGJ416jXWN/Y2zjVcZHzXuMaGb+JoITVaZHDN5ztBhsBi5jApGG6PP1NA01FRqusm0w/SzmbVZolmxWYPZPXOKOdM8w3yVeat5n4WJRaTFDIs6i9uWZEumZZblGsszlu+trK2SrRZYNVk9s9az5lgXWddZ37Wh2vjZTLapsblqS7Rl2ubYrrO9ZIfaudll2VXZXbRH7d3thfbr7DtHEEZ4jhCNqBlxw0HNgeVQ6FDn8MBR1zHCsdixyfHlSIuRqSOXjzwz8puTm1Ou0xanO6O0RoWNKh7VMuq1s50zz7nK+aoL1SXYZbZLs8srV3tXget615tudLdItwVurW5f3T3cJe717j0eFh5pHtUeN5jazBjmYuZZT4JngOdsz8OeH73cvQq89nr95e3gneO9w/vZaOvRgtFbRj/yMfPh+mzy6fJl+Kb5bvTt8jP14/rV+D30N/fn+2/1f8qyZWWzdrJeBjgFSAIOBLxne7Fnso8HYoEhgaWBHUFaQYlBlUH3g82CM4PrgvtC3EKmhxwPJYSGhy4PvcEx4vA4tZy+MI+wmWFt4Wrh8eGV4Q8j7CIkES2RaGRY5MrIu1GWUaKopmgQzYleGX0vxjpmcsyhWGJsTGxV7JO4UXEz4s7E0+Mnxu+If5cQkLA04U6iTaI0sTWJljQuqTbpfXJg8orkrjEjx8wccyHFIEWY0pxKSk1K3ZraPzZo7Oqx3ePcxpWMuz7eevzU8ecmGEzInXBkIm0id+K+NEJactqOtC/caG4Ntz+dk16d3sdj89bwXvD9+av4PQIfwQrB0wyfjBUZzzJ9Mldm9mT5ZZVn9QrZwkrhq+zQ7A3Z73Oic7blDOQm5zbkqeSl5R0UaYlyRG2TjCdNndQptheXiLsme01ePblPEi7Zmo/kj89vLtCGH/LtUhvpL9IHhb6FVYUfpiRN2TdVc6poavs0u2mLpj0tCi76bTo+nTe9dYbpjLkzHsxkzdw0C5mVPqt1tvns+bO754TM2T6XMjdn7u/FTsUrit/OS57XMt9o/pz5j34J+aWuRL1EUnJjgfeCDQvxhcKFHYtcFq1d9K2UX3q+zKmsvOzLYt7i87+O+rXi14ElGUs6lrovXb+MuEy07Ppyv+XbV2iuKFrxaGXkysZVjFWlq96unrj6XLlr+YY1lDXSNV0VERXNay3WLlv7pTKr8lpVQFVDtWH1our36/jrLq/3X1+/wWhD2YZPG4Ubb24K2dRYY1VTvpm4uXDzky1JW878xvytdqvB1rKtX7eJtnVtj9veVutRW7vDcMfSOrROWtezc9zOS7sCdzXXO9RvatBtKNsNdkt3P9+Ttuf63vC9rfuY++r3W+6vPkA/UNqINE5r7GvKaupqTmnuPBh2sLXFu+XAIcdD2w6bHq46onNk6VHK0flHB44VHes/Lj7eeyLzxKPWia13To45ebUttq3jVPips6eDT588wzpz7KzP2cPnvM4dPM8833TB/UJju1v7gd/dfj/Q4d7ReNHjYvMlz0stnaM7j172u3ziSuCV01c5Vy9ci7rWeT3x+s0b42503eTffHYr99ar24W3P9+Zc5dwt/Sexr3y+4b3a/6w/aOhy73ryIPAB+0P4x/eecR79OJx/uMv3fOfUJ+UPzV5WvvM+dnhnuCeS8/HPu9+IX7xubfkT80/q1/avNz/l/9f7X1j+rpfSV4NvF78Rv/Ntreub1v7Y/rvv8t79/l96Qf9D9s/Mj+e+ZT86ennKV9IXyq+2n5t+Rb+7e5A3sCAmCvhyj8FMFjRjAwAXm8DgJoCAB2ezyhjFec/eUEUZ1Y5Av8JK86I8uIOQD38fo/thV83NwDYvQUev6A+bRwAMVQAEjwB6uIyVAfPavJzpawQ4TlgY9TX9Lx08G+K4sz5Q9w/t0Cm6gp+bv8FLId8Rf1Y+w4AAAA4ZVhJZk1NACoAAAAIAAGHaQAEAAAAAQAAABoAAAAAAAKgAgAEAAAAAQAAASigAwAEAAAAAQAAASgAAAAAfSAh0gAADRRJREFUeAHt3ctubLcVBFAn8P//cuKBL3AHNquhUolH6pVJglD7wXWcQg8I5I8//IsAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIDA+wr85wFX/98Ddliu0Bonn9S/rW9t0vzUP90v1a/nt/3T/rfPW/9q//9W1YoJECAwFBBQQ1ytCRDoBARU56eaAIGhgIAa4mpNgEAnIKA6P9UECAwFBNQQV2sCBDoBAdX5qSZAYCjw57D3Z7W++g7jhUvcfgeT5rd+6/6390v3e+EfgeOftPc7Nv+Ew/X9qxX9gqr4FBMgsBQQUEtdvQkQqAQEVMWnmACBpYCAWurqTYBAJSCgKj7FBAgsBQTUUldvAgQqAQFV8SkmQGAp8B3eQaX7r99x3H7Hkuav75/80/l6/3X/dL/2fP39kk+7/7TeL6gpr+YECDQCAqrRU0uAwFRAQE15NSdAoBEQUI2eWgIEpgICasqrOQECjYCAavTUEiAwFRBQU17NCRBoBH7CO6jm/k+oTe9g0juWdL6+Y9o/zU/7r/un/ZxfFPAL6iK+0QQInAUE1NnHKQECFwUE1EV8owkQOAsIqLOPUwIELgoIqIv4RhMgcBYQUGcfpwQIXBQQUBfxjSZA4CzgHdTZ5ytO0zugdof0jmg9P/Vv92vrW1/1QwG/oIa4WhMg0AkIqM5PNQECQwEBNcTVmgCBTkBAdX6qCRAYCgioIa7WBAh0AgKq81NNgMBQQEANcbUmQKAT+AnvoNI7m05oX53e8aQN0v3TeZrf1qf903naL9U//Tz5Pn3/6X5+QU15NSdAoBEQUI2eWgIEpgICasqrOQECjYCAavTUEiAwFRBQU17NCRBoBARUo6eWAIGpgICa8mpOgEAj8B3eQf30dzDp+6V3Mskn1af5t8/T/un+af9Un+a3/VP9W5/7BfXWn9/lCTxbQEA9+/vYjsBbCwiot/78Lk/g2QIC6tnfx3YE3lpAQL3153d5As8WEFDP/j62I/DWAgLqrT+/yxN4tkD7xuPZt/se26V3OOkW7Tds56f90nnaP+3X1rf7pXrnhYBfUAWeUgIEtgICauurOwEChYCAKvCUEiCwFRBQW1/dCRAoBARUgaeUAIGtgIDa+upOgEAhIKAKPKUECGwF0huS7fTXuqd3MKnL+o5pv3Z+6r++/3r+un/ySfPT90v1aX7bP9Wn+Y8+9wvq0Z/HcgTeW0BAvff3d3sCjxYQUI/+PJYj8N4CAuq9v7/bE3i0gIB69OexHIH3FhBQ7/393Z7AowUE1KM/j+UIvLfAd/j/xVt/ofU7lrR/Oz/1T+dpfnpnk+rT/HX/NP+nn6+/z9TPL6gpr+YECDQCAqrRU0uAwFRAQE15NSdAoBEQUI2eWgIEpgICasqrOQECjYCAavTUEiAwFRBQU17NCRBoBL7DO6j0TibdP70DeXr/dL/2vL1/W7/+Pq1Pqk/3T/db91/vl/avzv2CqvgUEyCwFBBQS129CRCoBARUxaeYAIGlgIBa6upNgEAlIKAqPsUECCwFBNRSV28CBCoBAVXxKSZAYCmQ3kgsZ//qnd6JPGHHX7t+5N/T/VLPdP/UP9Wn+e357f3S/PZ+t+tvf9/p/f2CmvJqToBAIyCgGj21BAhMBQTUlFdzAgQaAQHV6KklQGAqIKCmvJoTINAICKhGTy0BAlMBATXl1ZwAgUbgCW8o3v2dyvr+T/jGp39G0/3X+6f5p90/4yzdL+23rv+MO364h19QH6ZTSIDAWkBArYX1J0DgwwIC6sN0CgkQWAsIqLWw/gQIfFhAQH2YTiEBAmsBAbUW1p8AgQ8LCKgP0ykkQGAt8B3+f/GSQfsOJPVP5+38tj7ttz5v3+m0+6X5qX/yT/Vpfts/zU/naf6j9/cLKn1e5wQIXBMQUNfoDSZAIAkIqCTknACBawIC6hq9wQQIJAEBlYScEyBwTUBAXaM3mACBJCCgkpBzAgSuCaQ3EtcW+0aDH/2O5BMcb98vzf+EKx5btP8bSfun/qn+uPxfh23/VJ/mV+d+QVV8igkQWAoIqKWu3gQIVAICquJTTIDAUkBALXX1JkCgEhBQFZ9iAgSWAgJqqas3AQKVgICq+BQTILAUuPrGYXmx33q370h+a/WP/7E1bPdL81P/tv4fUX77L1P/3/70Q/8x3e9DTT+xKN2/3T/1/8SrfH0rv6C+3txEAgReFBBQL0L5MwIEvl5AQH29uYkECLwoIKBehPJnBAh8vYCA+npzEwkQeFFAQL0I5c8IEPh6AQH19eYmEiDwosAT3lB893cgaf/WuO2/rn/xH7Vrf5b8b/uk/Vq4dL/Uf73fcb5fUEcehwQI3BQQUDf1zSZA4CggoI48DgkQuCkgoG7qm02AwFFAQB15HBIgcFNAQN3UN5sAgaOAgDryOCRA4KbA1TcOf188vdNIO96uT9+v3a/tn+rb8/R9Uv/Wp61v90v1t33Sfmu/NP947hfUkcchAQI3BQTUTX2zCRA4CgioI49DAgRuCgiom/pmEyBwFBBQRx6HBAjcFBBQN/XNJkDgKCCgjjwOCRC4KdC+0bi5+6/Z6R3Hr7+79e/JuN1/3b91a/dL9Wm/5Nv2T/PTedov1afzdL80P9Wn+dW5X1AVn2ICBJYCAmqpqzcBApWAgKr4FBMgsBQQUEtdvQkQqAQEVMWnmACBpYCAWurqTYBAJSCgKj7FBAgsBa6+cfj7YukdRnv/9o5pv9R/Xd/6pPr2fql/Or89P+2XztP+qX79z0+7X9q/OvcLquJTTIDAUkBALXX1JkCgEhBQFZ9iAgSWAgJqqas3AQKVgICq+BQTILAUEFBLXb0JEKgEBFTFp5gAgaXAo99AvHjx9TuRF9eY/Vn6Rrfvn/ZLMO3+bf9U//Tz5L/2nfr4BTXl1ZwAgUZAQDV6agkQmAoIqCmv5gQINAICqtFTS4DAVEBATXk1J0CgERBQjZ5aAgSmAgJqyqs5AQKNQHpD0fRW+5pAeqeSutz+hu3+6X7pvL1/2r/tn/ZP81N92m/dP+1XnfsFVfEpJkBgKSCglrp6EyBQCQioik8xAQJLAQG11NWbAIFKQEBVfIoJEFgKCKilrt4ECFQCAqriU0yAwFLgz2XzF3u37zReHHPtz9I7lXSeFv/ufu39k8/6PPmv79fOT/Vrv2N/v6COPA4JELgpIKBu6ptNgMBRQEAdeRwSIHBTQEDd1DebAIGjgIA68jgkQOCmgIC6qW82AQJHAQF15HFIgMBNgSe8g0r3X78jSfPT+fodSdu/9UvzU/9U3/qu56f90vxU356n+ck/1bf7VfV+QVV8igkQWAoIqKWu3gQIVAICquJTTIDAUkBALXX1JkCgEhBQFZ9iAgSWAgJqqas3AQKVgICq+BQTILAU+A7voNL90zuPVJ/OH/1OJC3/13nyae+37v/CFad/ku43Hf5X8/b7rPeb9vcLasqrOQECjYCAavTUEiAwFRBQU17NCRBoBARUo6eWAIGpgICa8mpOgEAjIKAaPbUECEwFBNSUV3MCBBqBn/AOqrn/T6h963cyn/ABk196B9XWt1dI+7X9r9b7BXWV33ACBE4CAuqk44wAgasCAuoqv+EECJwEBNRJxxkBAlcFBNRVfsMJEDgJCKiTjjMCBK4KCKir/IYTIHAS8A7qpPM1Z+kdS3pn026Z5q/7t/dL+6f+qb69/9Pr0/2T3/R+fkFNeTUnQKAREFCNnloCBKYCAmrKqzkBAo2AgGr01BIgMBUQUFNezQkQaAQEVKOnlgCBqYCAmvJqToBAI/AT3kFdfafR4L9Ye/udSvJd79fOf5H5X/8szf/Xwr8P2vq1b9r/6rlfUFf5DSdA4CQgoE46zggQuCogoK7yG06AwElAQJ10nBEgcFVAQF3lN5wAgZOAgDrpOCNA4KqAgLrKbzgBAieB9o3GqferZ+mdx6t9nvp3rfFtn9v7t/PTPxetb9qv7Z/2T/NTfdqv7Z/mH8/9gjryOCRA4KaAgLqpbzYBAkcBAXXkcUiAwE0BAXVT32wCBI4CAurI45AAgZsCAuqmvtkECBwFBNSRxyEBAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIELgj8H+416fofk5+DAAAAABJRU5ErkJggg=="/>
    </defs>
</svg>
