export const GATEWAY_BASIC_AUTHORIZATION_USERNAME = import.meta.env.VITE_GATEWAY_BASIC_AUTHORIZATION_USERNAME;
export const API_GATEWAY_URL = import.meta.env.VITE_API_GATEWAY_BASE_URL;
export const PROJECT_ID = import.meta.env.VITE_PROJECT_ID;
export const API_REPO_NAME = import.meta.env.VITE_PRISMIC_REPO_NAME;
export const API_GATEWAY_AUTH_PROXY_URL = import.meta.env.VITE_API_GATEWAY_AUTH_PROXY_BASE_URL;
export const API_GATEWAY_AUTH_URL = import.meta.env.VITE_API_GATEWAY_AUTH_BASE_URL;
export const CLEVERTAP_KEY = import.meta.env.VITE_CLEVERTAP_KEY;
export const PRISMIC_REPO_NAME = import.meta.env.VITE_PRISMIC_REPO_NAME;
export const MIXPANEL_PROJECT_TOKEN = import.meta.env.VITE_MIXPANEL_PROJECT_TOKEN;

export const MEDPLUM_API_URL = `${API_GATEWAY_URL}/emr`;
export const MEDPLUM_GRAPHQL_API_URL = `${API_GATEWAY_URL}/emr/fhir/R4/$graphql`;
export const ELASTIC_SEARCH_API_URL = `${API_GATEWAY_URL}/search`;
export const MEDPLUM_CARE_TEAM_API_URL = `${API_GATEWAY_URL}/emr/fhir/R4/CareTeam`;
export const MEDPLUM_GRAPHQL_SHARE_API_URL = `${API_GATEWAY_URL}/emr/share/R4/$graphql`;
export const MED_PLUM_API_URL_REST = `${API_GATEWAY_URL}/emr/fhir/R4`;
export const CMS_GRAPHQL_API_URL = `${API_GATEWAY_URL}/cms/graphql`;
export const CONTENT_LIBRARY_API_URL = `${API_GATEWAY_URL}/content-library/v1`;

export const FHIR_URL = 'https://fluentinhealth.com/FHIR';
export const FHIR_VALUE_SET_URL = `${FHIR_URL}/ValueSet`;
export const SNOMED_MEDICATION_FHIR = `${FHIR_VALUE_SET_URL}/SnomedMedicationNames`;
export const FHIR_VALUE_SET_FACT_URL = `${FHIR_VALUE_SET_URL}/FACT`;
export const FHIR_STRUCTURE_DEFINITION_URL = `${FHIR_URL}/StructureDefinition`;
export const FHIR_CODE_SYSTEM_URL = `${FHIR_URL}/CodeSystem`;
export const FHIR_CODE_SYSTEM_FACT_URL = `${FHIR_CODE_SYSTEM_URL}/FACT`;
export const MEDPLUM_QUESTIONNAIRE = `${FHIR_URL}/Questionnaire`;

export const SHARED_STATE = {
  IS_LOGGED_IN: 'is_logged_in',
};

export const BREAKPOINTS = {
  MOBILE: '(min-width: 0px)',
  TABLET: '(min-width: 767px)',
  DESKTOP: '(min-width: 1023px)',
};

export const LOCAL_STORAGE_KEYS = {
  PATIENT_PROGRESS: 'fh_patient_progress',
  APP_SETTINGS: 'app_static_settings',
  FAQS: 'faqs',
};

// For a public page, we need to store a temporary token as a query parameter.
// This constant contains the query key for the token.
export const TEMPORARY_TOKEN_QUERY_KEY = 'temporary_token';
export const PUBLIC_URL_PREFIX = '/public';
export const SHARE_PROFILE_URL_PREFIX = '/share-profile';
export const SHARE_RECORD_URL_PREFIX = '/share-records';

export const HOURLY_BASE_OPTIONS = [...Array(24).keys()].map((el) => {
  const hour12 = el % 12 || 12;
  const period = el < 12 ? 'am' : 'pm';
  const label = `${hour12}:00 ${period}`;
  const value = `${el.toString().padStart(2, '0')}:00`;

  return {
    label,
    value,
    hideIcon: true,
  };
});

export const LOGIN_MODES = {
  GENERATE: 'generate',
  VERIFY: 'verify',
};

export const TOKEN_GRANT_TYPES = {
  CLIENT_CREDENTIALS: 'client_credentials',
};

export enum enumTaxonomyQuery {
  PROCEDURES = '?fhir_vs=ecl/<71388002&filter=',
  SYMPTOMS = '?fhir_vs=ecl/<*********&filter=',
  ALLERGIES_AND_OR_INTOLERANCES_CATEGORY = 'http://hl7.org/fhir/ValueSet/allergy-intolerance-category',
  ALLERGIES_AND_OR_INTOLERANCES_CRITICALITY = 'http://hl7.org/fhir/ValueSet/allergy-intolerance-criticality',
  MEDICATION_AND_SUPPLEMENT = '/1121000189102/version/20240715?fhir_vs=ecl/<<*********&filter=',
  CONDITIONS = '?fhir_vs=ecl/<64572001&filter=',
}
export enum FACT_CODE_SYSTEM {
  CONDITIONS = 'FACT-Category-cn',
  CONDITIONS_COURSE = 'Condition-course',
  SYMPTOMS = 'FACT-Category-sym',
  PROCEDURES = 'FACT-Category-pro',
  PROCEDURES_STATUS = 'ProcedureStatus',
  BLOODPRESSURE_POSITION = 'RecordedPosition',
  AllergyIntolerance = 'FACT-Category-al',
  Allergies_Intolerances_Category = 'AllergiesIntolerancesCategory',
  ALLERGIES_INTOLERANCES_CRITICALITY = 'AllergiesIntolerancesCriticality',
  Allergies_Intolerances_STATUS = 'AllergiesIntolerancesClinicalStatus',
  IMMUNIZATION = 'FACT-Category-im',
}
export const DOCUMENT_REFERENCE_QUERY_FRAGMENT = `
  resource {
    ... on DocumentReference {
      id description status docStatus
      type { coding { display code system } }
      extension { url valueString }
      subject { reference resource { ... on Patient { id name { family prefix given use } } ... on Practitioner { id name { family prefix given use } } } }
      context { period { start end } encounter { reference resource { ... on Encounter { id class { code display system } } } } related { reference resource { ... on DiagnosticReport { __typename id status code { coding { code display system } } subject { reference } issued performer { reference } result { reference resource { ... on Observation { id status code { coding { code display system } } subject { reference } component { code { coding { code system display } } } valueQuantity { value unit system code } referenceRange { low { value unit } high { value unit } } } } } } ... on Practitioner { __typename id name { text family given prefix } } ... on PractitionerRole { __typename id specialty { coding { code display system } } } ... on Condition { __typename id conditionCode: code { coding { display code system } } } ... on Organization { __typename id hospitalName: name } } } }
      content { attachment { url size title creation contentType } }
      meta { tag { code display system } }
    }
  }`;

export const INVALIDATE_DELAY = 1000;

export const deleteIdentifier = 'urn:fh-workflow:task:delete';
export const downloadIdentifier = 'urn:fh-workflow:task:download:alldata';
export const documentReferenceTags = 'urn:fh-workflow:task:documentReferenceTags';

// clever tap
export const cleverTapEventId = 'ct-fh-notify';
export const CACHE_NAME = 'clevertap-cache-v1';
export function toCapitalize(str: string): string {
  if (!str) return '';
  return str
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}
