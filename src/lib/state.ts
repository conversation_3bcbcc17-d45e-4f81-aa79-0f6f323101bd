// Package modules
import { useEffect, useMemo, useRef, useState } from 'react';
import { useInfiniteQuery, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useDebounce } from 'usehooks-ts';
// Local modules
import { Client, useClient } from 'urql';
import { medplumApi } from '@user/lib/medplum-api';
import { useParams, useSearchParams } from 'react-router-dom';
import { Buffer } from 'buffer';
import { DOCUMENT_REF } from '@user/lib/constants';

import { api } from '@lib/api';
// import { api as userApi } from '../app/user/lib/api';
import { Patient } from '@lib/models/patient';
import { AuthService } from './authService';
import { PUBLIC_URL_PREFIX, SHARED_STATE, SHARE_PROFILE_URL_PREFIX, SHARE_RECORD_URL_PREFIX } from '@lib/constants';
import { useIntersected } from '@lib/utils/utils';
import { AnalyticsService } from '@lib/analyticsService';
import { Alert, AlertPayload } from '@lib/models/alert';
// import { PaginatedResponse } from '@lib/models/paginated-response';
// import { PUBLIC_SETTINGS_PROPERTY_NAME, PublicProfileDetails } from '@lib/models/public-settings';
// import { Allergy } from '@lib/models/allergies';
// import { Condition } from '@lib/models/condition';
// import { EmergencyContact } from '@lib/models/emergency-contact';
// import { HealthcareProxy } from '@lib/models/healthcare-proxy';
// import { HealthInsurance } from '@lib/models/health-insurance';
// import { Immunization } from '@lib/models/immunization';
// import { PatientLifestyleAnswer } from '@lib/models/lifestyle-and-nutrition';
// import { Medication } from '@lib/models/medication';
// import { PatientVitals } from '@lib/models/patient-vitals';
// import { RelatedPerson } from '@lib/models/related-person';
// import { PatientReproductiveHealthAnswer } from '@lib/models/reproductive-health';
// import { Screening } from '@lib/models/screening';
// import { PastSurgery } from '@lib/models/surgery-proceudure';
// import { Symptom } from '@lib/models/symptoms';
import { COMM_REMINDER_EXTENSION } from 'src/app/medical-records/lib/constants';
import { getAllAlertsQuery, getDocumentReference, getFileFromTask } from './medplum-graphql-query';

// Constants
export const STATE_TYPES = {
  ...SHARED_STATE,
  ALERT: 'alert',
  ALERT_LIST: 'alert_list',
  PUBLIC_SETTINGS: 'public_settings',
  CONSENT_MANAGER: 'consent_manager',
};

export const useAnalyticsService = <T>() => {
  const analyticsService = AnalyticsService.instance;
  const [analyticsData, setAnalyticsData] = useState<T>();

  return useMemo(
    () => ({
      analyticsService,
      trackEvent: analyticsService.trackEvent.bind(analyticsService),
      trackEventInFlow: analyticsService.trackEventInFlow.bind(analyticsService),
      analyticsData,
      setAnalyticsData,
    }),
    [analyticsService, analyticsData]
  );
};

// function createPaginatedObject<T>(dataList: T[]): PaginatedResponse<T> {
//   return {
//     data: dataList,
//     total: dataList.length,
//     current_page: 1,
//     last_page: 1,
//     per_page: dataList.length,
//     from: 0,
//     to: 0,
//   };
// }

export const base64toJSON = (data: any) => {
  // console.log(data);
  const x: string = Buffer.from(data, 'base64').toString();
  const y: any = JSON.parse(x);
  // console.log('DR b64J', x, y);
  return y;
};

const segregateData = (arrData: any, checkStr: string) => {
  const x = arrData?.[0]?.section?.find((val: any) => val?.title === checkStr);

  if (!x) {
    return false;
  }

  if (checkStr !== 'Consent') {
    // console.log('Entry', x, `x${checkStr === 'HealthInsurance'}`);
    const y =
      checkStr === DOCUMENT_REF
        ? x?.entry[0].resource.content[0].attachment.data
        : x?.entry[0].resource?.entry[0].resource.content[0].attachment.data;
    return base64toJSON(y);
  }
  return x?.entry[0].resource.provision.data;
};

export const usePublicSettings: any = () => {
  // const { authenticatedUser, temporaryToken } = useAuthService();
  const isPublicMode: boolean =
    window.location.pathname.startsWith(PUBLIC_URL_PREFIX) ||
    window.location.pathname.startsWith(SHARE_PROFILE_URL_PREFIX);

  const isPublicRecordMode: boolean =
    window.location.pathname.startsWith(PUBLIC_URL_PREFIX) ||
    window.location.pathname.startsWith(SHARE_RECORD_URL_PREFIX);

  if (isPublicRecordMode) {
    return { isPublicMode };
  }
  const { shareId: consentId } = useParams();
  const [searchParams] = useSearchParams();

  const getPublicUrl = () => {
    return medplumApi.shareProfileRecord.getShareData(consentId, searchParams.get('access_token'), 'profile');
  };

  const { data }: any = useQuery<any>([STATE_TYPES.PUBLIC_SETTINGS, consentId], getPublicUrl, {
    // PublicProfileDetails
    // Retrieve data from the database only on a public page
    enabled: isPublicMode,
    staleTime: 1000,
    onError: (error) => {
      throw error;
    },
  });

  const myPatient = data ? segregateData(data, 'Patient')?.data?.Patient : {};

  const myConditions = data ? segregateData(data, 'Condition')?.data?.ConditionList : [];

  const myMedications = data ? segregateData(data, 'Medication')?.data?.QuestionnaireResponseList : [];

  const mySupplement = data ? segregateData(data, 'Supplement')?.data?.QuestionnaireResponseList : [];

  const mySymptoms = data ? segregateData(data, 'Symptom')?.data?.QuestionnaireResponseList : [];

  const myPastSurgeries = data ? segregateData(data, 'Procedure')?.data?.QuestionnaireResponseList : [];

  const myAllergies = data ? segregateData(data, 'AllergyIntolerance')?.data?.QuestionnaireResponseList : [];

  const myEmergencyContacts = data ? segregateData(data, 'EmergencyContacts')?.data?.RelatedPersonList : [];

  const myHealthcareProxy = data ? segregateData(data, 'HealthcareProxy')?.data?.RelatedPersonList : [];

  const myHealthInsurances = data ? segregateData(data, 'HealthInsurance')?.data : [];

  const myProcedureList = data ? segregateData(data, 'ProcedureList')?.data : [];

  const myRelatedPersons = data ? segregateData(data, 'RELATED_PERSONS')?.data?.RelatedPersonList : [];

  const myCareTeam = data ? segregateData(data, 'CareTeam')?.data?.CareTeamList : [];

  // LifeStyle and Nutrition
  const myLifestyleNutritionOccupation = data
    ? segregateData(data, 'LifestyleNutritionOccupation')?.data?.QuestionnaireResponseList
    : [];

  const myLifestyleNutritionAlcoholTobaccoCaffeine = data
    ? segregateData(data, 'LifestyleNutritionAlcoholTobaccoCaffeine')?.data?.QuestionnaireResponseList
    : [];

  const myLifestyleNutritionMentalHealthSleep = data
    ? segregateData(data, 'LifestyleNutritionMentalHealthSleep')?.data?.QuestionnaireResponseList
    : [];

  const myLifestyleNutritionDiet = data
    ? segregateData(data, 'LifestyleNutritionDiet')?.data?.QuestionnaireResponseList
    : [];

  const myLifestyleNutritionExercise = data
    ? segregateData(data, 'LifestyleNutritionExercise')?.data?.QuestionnaireResponseList
    : [];

  // Reproductive Health
  const myPatientReproductiveHealthAnswers = data
    ? segregateData(data, 'ReproductiveHealth')?.data?.QuestionnaireResponseList
    : [];

  // Preventative Screening
  const myPreventativeScreening = data
    ? segregateData(data, 'PreventativeScreening')?.data?.QuestionnaireResponseList
    : [];
  const myPSAnnualPhysical = data ? segregateData(data, 'PSAnnualPhysical')?.data?.QuestionnaireResponseList : [];
  const myPSLipidProfile = data ? segregateData(data, 'PSLipidProfile')?.data?.QuestionnaireResponseList : [];
  const myPSPapSmear = data ? segregateData(data, 'PSPapSmear')?.data?.QuestionnaireResponseList : [];
  const myPSMammogram = data ? segregateData(data, 'PSMammogram')?.data?.QuestionnaireResponseList : [];
  const myPSBreastSelfEvaluation = data
    ? segregateData(data, 'PSBreastSelfEvaluation')?.data?.QuestionnaireResponseList
    : [];
  const myPSSkinSelfExamination = data
    ? segregateData(data, 'PSSkinSelfExamination')?.data?.QuestionnaireResponseList
    : [];
  const myPSDentalCheckupCleaning = data
    ? segregateData(data, 'PSDentalCheckupCleaning')?.data?.QuestionnaireResponseList
    : [];
  const myPSEyeExamination = data ? segregateData(data, 'PSEyeExamination')?.data?.QuestionnaireResponseList : [];
  const myPSElectrocardiogramECG = data
    ? segregateData(data, 'PSElectrocardiogramECG')?.data?.QuestionnaireResponseList
    : [];
  const myPSTreadmillTestTMT = data ? segregateData(data, 'PSTreadmillTestTMT')?.data?.QuestionnaireResponseList : [];
  const myPS2DEchocardiogram = data ? segregateData(data, 'PS2DEchocardiogram')?.data?.QuestionnaireResponseList : [];

  // Immunization
  const myImmunizations = data ? segregateData(data, 'Immunization')?.data?.QuestionnaireResponseList : [];
  const myImmunizationFlu = data ? segregateData(data, 'ImmunizationFlu')?.data?.QuestionnaireResponseList : [];
  const myImmunizationCovid19 = data ? segregateData(data, 'ImmunizationCovid19')?.data?.QuestionnaireResponseList : [];
  const myImmunizationHepatitisA = data
    ? segregateData(data, 'ImmunizationHepatitisA')?.data?.QuestionnaireResponseList
    : [];
  const myImmunizationHepatitisB = data
    ? segregateData(data, 'ImmunizationHepatitisB')?.data?.QuestionnaireResponseList
    : [];
  const myImmunizationMeaslesMumpsRubellaMMR = data
    ? segregateData(data, 'ImmunizationMeaslesMumpsRubellaMMR')?.data?.QuestionnaireResponseList
    : [];
  const myImmunizationPneumonia = data
    ? segregateData(data, 'ImmunizationPneumonia')?.data?.QuestionnaireResponseList
    : [];
  const myImmunizationTDap = data ? segregateData(data, 'ImmunizationTDap')?.data?.QuestionnaireResponseList : [];
  const myImmunizationTuberculosis = data
    ? segregateData(data, 'ImmunizationTuberculosis')?.data?.QuestionnaireResponseList
    : [];
  const myImmunizationVaricella = data
    ? segregateData(data, 'ImmunizationVaricella')?.data?.QuestionnaireResponseList
    : [];
  const myConsent = data ? segregateData(data, 'Consent') : [];
  const myFamilyMemberHistory = data ? segregateData(data, 'FamilyMemberHistory')?.data : [];
  const myConsentFeedback = data ? segregateData(data, 'ConsentFeedback')?.data?.QuestionnaireResponseList : [];
  const mySymptom = data ? segregateData(data, 'ObservationList')?.data : [];
  return {
    settings: data ? data[0] : {},
    isPublicMode,
    myConsent,
    myPatient,
    myEmergencyContacts,
    myHealthcareProxy,
    myHealthInsurances,
    myProcedureList,
    myRelatedPersons,
    myCareTeam,
    myConditions,
    myMedications,
    mySupplement,
    mySymptoms,
    myPastSurgeries,
    myAllergies,
    myFamilyMemberHistory,
    myConsentFeedback,
    mySymptom,
    // LifeStyle and Nutrition
    myLifestyleNutritionOccupation,
    myLifestyleNutritionAlcoholTobaccoCaffeine,
    myLifestyleNutritionMentalHealthSleep,
    myLifestyleNutritionDiet,
    myLifestyleNutritionExercise,

    // Reproductive Health
    myPatientReproductiveHealthAnswers,

    // Preventative Screening
    myPreventativeScreening,
    myPSAnnualPhysical,
    myPSLipidProfile,
    myPSPapSmear,
    myPSMammogram,
    myPSBreastSelfEvaluation,
    myPSSkinSelfExamination,
    myPSDentalCheckupCleaning,
    myPSEyeExamination,
    myPSElectrocardiogramECG,
    myPSTreadmillTestTMT,
    myPS2DEchocardiogram,

    // Immunization
    myImmunizations,
    myImmunizationFlu,
    myImmunizationCovid19,
    myImmunizationHepatitisA,
    myImmunizationHepatitisB,
    myImmunizationMeaslesMumpsRubellaMMR,
    myImmunizationPneumonia,
    myImmunizationTDap,
    myImmunizationTuberculosis,
    myImmunizationVaricella,
  };
};

export const usePublicRecordSettings = () => {
  const isPublicMode: boolean =
    window.location.pathname.startsWith(PUBLIC_URL_PREFIX) ||
    window.location.pathname.startsWith(SHARE_PROFILE_URL_PREFIX);

  const isPublicRecordMode: boolean =
    window.location.pathname.startsWith(PUBLIC_URL_PREFIX) ||
    window.location.pathname.startsWith(SHARE_RECORD_URL_PREFIX);

  if (isPublicMode) {
    return { isPublicRecordMode };
  }

  const { shareId: consentId } = useParams();
  const [searchParams] = useSearchParams();

  const getPublicRecordUrl = () => {
    return medplumApi.shareProfileRecord.getShareData(consentId, searchParams.get('access_token'), 'record');
  };

  const { data }: any = useQuery<any>([STATE_TYPES.PUBLIC_SETTINGS, consentId], getPublicRecordUrl, {
    // PublicProfileDetails
    // Retrieve data from the database only on a public page
    enabled: isPublicRecordMode,
    staleTime: 1000,
    onError: (error) => {
      throw error;
    },
  });

  const myDocumentReferenceList = data?.section?.filter((val: any) => val?.title === DOCUMENT_REF)?.[0]?.entry || [];

  const myRecordPatient = data?.section?.find((val: any) => val?.title === 'Patient')?.entry[0].resource || {};

  return {
    isPublicRecordMode,
    myDocumentReferenceList,
    myRecordPatient,
    isMultiRecord: myDocumentReferenceList.length > 1,
  };
};

export const useAuthService = () => {
  const authService = AuthService.instance;
  const authenticatedUser = authService.getAuthenticatedUser();
  const { temporaryToken } = AuthService;
  const { isPublicMode } = usePublicSettings();
  const { isPublicRecordMode } = usePublicRecordSettings();

  const { data: isLoggedIn, refetch: verifyMe } = useQuery(
    [STATE_TYPES.IS_LOGGED_IN],
    async () => {
      let isAuthenticated = false;
      if (!isPublicMode && !isPublicRecordMode) {
        isAuthenticated = await AuthService.instance.isLoggedIn();
      }
      return isAuthenticated;
    },
    {
      staleTime: 60000,
      suspense: true,
    }
  );

  const staticProps = useMemo(
    () => ({
      authService,
      login: authService.login.bind(authService),
      logout: authService.logout.bind(authService),
      loginVerify: authService.loginVerify.bind(authService),
      OTP2FAVerify: authService.OTP2FAVerify.bind(authService),
      resendCode: authService.resetPinGenerateOTP.bind(authService),
      resetPinGenerateOTP: authService.resetPinGenerateOTP.bind(authService),
      resetPinOTPVerify: authService.resetPinOTPVerify.bind(authService),
      resetPinSave: authService.resetPinSave.bind(authService),
      setAuthenticatedUser: authService.setAuthenticatedUser.bind(authService),
    }),
    [authService]
  );
  return {
    isLoggedIn,
    verifyMe,
    authenticatedUser,
    temporaryToken,
    ...staticProps,
  };
};

export async function getAlerts(client: Client, variables: any) {
  const response = await client.query(getAllAlertsQuery, variables);

  return response?.data?.CommunicationRequestList ?? [];
}

export const useAlertList = (patientId: Patient['id']) => {
  const queryClient = useQueryClient();
  const client = useClient();

  const queryKey = [STATE_TYPES.ALERT_LIST, patientId];
  const getAll = () => {
    return getAlerts(
      client,
      { patientId, reminderUrl: COMM_REMINDER_EXTENSION }
      // {
      // ...filters,
      // page: pageParam,}}
    );
  };

  const { data, fetchNextPage, hasNextPage, isLoading, isFetching } = useInfiniteQuery(queryKey, getAll, {
    // getNextPageParam: (lastPage) =>
    //   lastPage.current_page < lastPage.last_page ? lastPage.current_page + 1 : undefined,
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
    staleTime: Infinity,
    suspense: false,
  });

  const addAlert = (payload: AlertPayload) => api.alerts.addAlert(payload);
  const { mutateAsync: mutateAddAlert, isLoading: isCreatingAlert } = useMutation(addAlert, {
    onSuccess: () => {
      // Refetch loaded pages to correctly display the created reminder.
      queryClient.invalidateQueries(queryKey);
    },
  });

  type UpdateAlertPayload = {
    alertId: Alert['id'];
    payload: AlertPayload;
  };
  const updateAlert = ({ alertId, payload }: UpdateAlertPayload) => api.alerts.updateAlert(alertId, payload);

  const linkCommRequestWithDocRef = ({
    commReqId,
    docRefId,
  }: {
    commReqId: string | null | undefined;
    docRefId: string;
  }) => api.alerts.linkCommRequestWithDocRef(commReqId, docRefId);

  const { mutateAsync: mutateUpdateAlert, isLoading: isUpdatingAlert } = useMutation(updateAlert, {
    onSuccess: async () => {
      queryClient.invalidateQueries(queryKey);
    },
  });

  const deleteAlert = (alertId: Alert['id']) => api.alerts.deleteAlert(alertId);
  const { mutateAsync: mutateDeleteAlert, isLoading: isDeletingAlert } = useMutation(deleteAlert, {
    onSuccess: () => {
      queryClient.invalidateQueries(queryKey);
    },
  });

  // Concatenate all data pages into a single array
  const alertList = Object.values(data?.pages?.[0] || {})?.flatMap((item) => item);
  return {
    alertList,
    linkCommRequestWithDocRef,
    addAlert: mutateAddAlert,
    updateAlert: mutateUpdateAlert,
    deleteAlert: mutateDeleteAlert,
    isMutating: isCreatingAlert || isUpdatingAlert || isDeletingAlert,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isLoading,
  };
};

export const useAlertListLazyPagination = (patientId: Patient['id']) => {
  const loadingElementRef = useRef(null);

  const { isIntersection: shouldLoadMore } = useIntersected(loadingElementRef);
  const debouncedShouldLoadMore = useDebounce(shouldLoadMore, 500);

  const { alertList, fetchNextPage, hasNextPage, isFetching, isLoading } = useAlertList(patientId);

  useEffect(() => {
    if (shouldLoadMore && hasNextPage && !isFetching) {
      fetchNextPage();
    }
  }, [shouldLoadMore, hasNextPage, isFetching]);

  return {
    alertList,
    loadingElementRef,
    shouldLoadMore: debouncedShouldLoadMore,
    hasNextPage,
    isLoading,
  };
};

export async function downloadProfile(client: Client, variables: any) {
  const response = await client.query(getFileFromTask, { id: variables });
  return response?.data?.Task ?? {};
}
export async function downloadFileQuery(client: Client, variables: string) {
  const response = await client.query(getDocumentReference, { id: variables });
  return response?.data?.DocumentReference ?? {};
}
